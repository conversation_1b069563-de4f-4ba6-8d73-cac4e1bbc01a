{"name": "api", "version": "1.14.0", "description": "superapi for lightning", "author": "sawyer", "license": "MIT", "scripts": {"build": "tsc -p tsconfig.build.json", "format": "prettier --write \"src/**/*.ts\"", "start": "ts-node -r tsconfig-paths/register src/main.ts ", "start:local": "NODE_ENV=test ts-node -r tsconfig-paths/register src/main.ts ", "start:dev": "nodemon", "start:debug": "nodemon --config nodemon-debug.json", "prestart:prod": "rimraf dist && tsc", "start:prod": "node dist/main.js", "lint": "tslint -p tsconfig.json -c tslint.json", "test": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:run && jest --forceExit", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "docs": "apidoc -i src -o docs"}, "dependencies": {"@google-cloud/firestore": "4.2.0", "@kubernetes/client-node": "0.16.3", "@nestjs/common": "5.5.0", "@nestjs/core": "5.5.0", "@nestjs/typeorm": "5.2.2", "base64url": "3.0.1", "big.js": "5.2.2", "cache-manager": "4.0.0", "cache-manager-redis-yet": "4.1.2", "class-transformer": "0.2.0", "class-validator": "0.11.1", "dotenv": "6.2.0", "handlebars": "4.5.3", "html-entities": "1.2.1", "i18n": "0.8.3", "joi": "14.3.0", "lodash": "4.17.11", "moment": "2.26.0", "mysql": "2.16.0", "node-jose": "1.1.0", "node-schedule": "1.3.2", "reflect-metadata": "0.1.12", "rimraf": "2.6.2", "rxjs": "6.3.3", "typeorm": "0.2.11", "typescript": "4.7.4", "uuid": "8.1.0", "yaml": "1.6.0"}, "devDependencies": {"@nestjs/testing": "5.5.0", "@types/express": "4.16.0", "@types/jest": "23.3.10", "@types/joi": "14.0.1", "@types/node": "14.18.10", "@types/supertest": "2.0.7", "@types/tar": "6.1.2", "apidoc": "0.50.5", "jest": "28.1.3", "minipass": "3.3.5", "nodemon": "1.18.3", "prettier": "1.14.2", "sinon": "7.2.3", "supertest": "3.1.0", "ts-jest": "28.0.7", "ts-loader": "4.4.2", "ts-node": "7.0.1", "tsconfig-paths": "3.5.0", "tslint": "5.11.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {".+\\.(t|j)s$": "ts-jest"}, "coverageDirectory": "../coverage", "testEnvironment": "node"}}