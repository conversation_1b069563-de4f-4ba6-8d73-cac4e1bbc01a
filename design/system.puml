@startuml
title System

package "Lightning" {
  [SuperApi]
  note left: <PERSON>
  [<PERSON>de <<https&trojan&vmess proxy>>]
  note left: Simon
  database "db: User info" as userInfoDb
}

actor "User" as user
component "Jss Api <<front-end api>>" as jss
note right: Echo William
component "WHMCS" as whmcs
note right: Mark
cloud "Firebase" <<Node info>> as fb
database "db: Service info" as serviceInfoDb


user -->> jss: user&service&product info
jss -->> whmcs: service and more info
whmcs -->> [SuperApi]: service CURD
whmcs -- serviceInfoDb
[SuperApi] -- userInfoDb
[SuperApi] -->> jss: service detail(usage&subscription)
[SuperApi] -->> user: node list = Node info + User info
user -->> [Node <<https&trojan&vmess proxy>>]: connect the proxy
fb -->> [SuperApi]: get
[SuperApi] -->> fb: update
[Node <<https&trojan&vmess proxy>>] -->> [SuperApi]: service auth&usage&online


package "Front end" {
    [Android]
    note bottom: <PERSON>
    [iOS]
    note bottom: Janice
    [Windows]
    note bottom: Fresh
    [Mac]
    note bottom: Fresh
    [Web]
    note bottom: Fresh
}
@enduml
