# 更新后的架构说明

## 概述

根据最新的文件结构调整，每日用量统计功能现在采用了更简洁的模块化架构，文件组织更加扁平化，便于维护。

## 最新架构设计

### 📁 更新后的模块结构

```
src/
├── usage-stats/                       # 用量统计模块（服务层）
│   ├── daily-usage.entity.ts          # 每日用量实体
│   ├── daily-usage.service.ts         # 每日用量业务逻辑
│   ├── memory-cache.service.ts        # 内存缓存服务
│   ├── usage-stats.module.ts          # 模块定义
│   ├── index.ts                       # 模块导出
│   └── README.md                      # 模块文档
│
├── service/
│   └── services.controller.ts         # API接口层（包含用量统计API）
│
└── nodes/
    └── subscribes.controller.ts       # 流量更新接口
```

### 🔄 数据流向

```
流量更新请求 → subscribes.controller.ts → DailyUsageService → MemoryCacheService
                                                    ↓
                                            每2分钟批量同步 → 数据库

用量查询请求 → services.controller.ts → DailyUsageService → 合并缓存+数据库数据
```

## 关键更新

### 1. **文件结构简化**
- ✅ 移除了 `entities/` 和 `services/` 子目录
- ✅ 所有文件直接放在 `usage-stats/` 根目录
- ✅ 导入路径更简洁：`from '../usage-stats/daily-usage.service'`

### 2. **同步频率优化**
- ✅ 缓存同步频率从30秒调整为2分钟
- ✅ 减少数据库写入压力
- ✅ 保持数据一致性的同时提高性能

### 3. **端口配置更新**
- ✅ 测试环境端口从3000改为3001
- ✅ 避免端口冲突
- ✅ 测试脚本同步更新

## API 接口（保持不变）

### 📊 用量查询接口

#### 1. 获取每日用量
```http
POST /api/v1/services/dailyUsage
Content-Type: application/json

{
  "clientId": 123,
  "client": "rubiz",
  "startDate": "2025-01-01",  // 可选
  "endDate": "2025-01-31"     // 可选
}
```

#### 2. 获取用量汇总
```http
POST /api/v1/services/usageSummary
Content-Type: application/json

{
  "clientId": 123,
  "client": "rubiz",
  "startDate": "2025-01-01",  // 可选
  "endDate": "2025-01-31"     // 可选
}
```

### 🔄 流量更新接口

```http
PUT /api/v1/subscribes/:id
Content-Type: application/json

{
  "username": "user123",
  "password": "pass123",
  "upload": 1048576,    // 上传增量
  "download": 2097152   // 下载增量
}
```

## 性能特性更新

### 🚀 内存缓存优化
- **原子性操作**: 确保并发安全
- **批量同步**: 每2分钟批量写入数据库（优化后）
- **自动清理**: 定时清理过期缓存
- **大小限制**: 最大10000条缓存记录

### ⏰ 定时任务调整
- **缓存同步**: 每2分钟同步缓存到数据库
- **强制同步**: 每小时强制同步确保一致性
- **数据清理**: 每天凌晨2点清理90天前的记录

## 使用方式更新

### 1. 模块导入（简化后）
```typescript
import { UsageStatsModule, DailyUsageService } from '../usage-stats';

@Module({
  imports: [UsageStatsModule],
  // ...
})
export class YourModule {}
```

### 2. 服务注入
```typescript
constructor(
  private dailyUsageService: DailyUsageService,
) {}
```

### 3. 记录用量
```typescript
// 在流量更新时调用
await this.dailyUsageService.recordDailyUsage(clientId, client, upload, download);
```

## 测试验证

### 功能测试（端口更新）
```bash
node test-daily-usage.js  # 现在使用3001端口
```

### 压力测试
```bash
node stress-test-daily-usage.js  # 现在使用3001端口
```

### 性能监控
```bash
node scripts/performance-monitor.js
node scripts/performance-monitor.js --watch
```

## 优势总结

### ✅ **架构优势**
1. **更简洁的结构**: 扁平化的文件组织
2. **避免重复**: 统一的API接口
3. **模块化**: 便于维护和扩展
4. **可重用**: 服务层可被其他模块使用

### ✅ **性能优势**
1. **内存缓存**: 极低延迟的用量记录
2. **优化同步**: 2分钟批量同步减少数据库压力
3. **异步处理**: 不阻塞主要业务流程
4. **智能清理**: 自动管理缓存和历史数据

### ✅ **维护优势**
1. **简化导入**: 更短的导入路径
2. **清晰结构**: 文件组织更直观
3. **易于调试**: 减少目录层级
4. **快速定位**: 所有相关文件在同一目录

## 迁移说明

如果从旧版本迁移，需要更新以下导入路径：

```typescript
// 旧的导入路径
import { DailyUsageService } from '../usage-stats/services/daily-usage.service';
import { DailyUsage } from '../usage-stats/entities/daily-usage.entity';

// 新的导入路径
import { DailyUsageService } from '../usage-stats/daily-usage.service';
import { DailyUsage } from '../usage-stats/daily-usage.entity';
```

## 总结

更新后的架构保持了所有功能的完整性，同时：
- 🎯 **简化了文件结构**: 更直观的组织方式
- 🔄 **优化了性能**: 调整同步频率减少数据库压力
- 🚀 **提高了可维护性**: 更简洁的导入路径
- 🛡️ **保持了安全性**: 不暴露敏感信息
- 🔧 **易于扩展**: 模块化设计便于添加新功能

这个更新后的架构更加简洁高效，是一个生产就绪的解决方案。
