const axios = require('axios');
const cluster = require('cluster');
const os = require('os');

// 配置
const CONFIG = {
  baseURL: 'http://localhost:3000',
  concurrentUsers: 20, // 降低并发数，适合内存缓存测试
  testDurationMinutes: 2, // 缩短测试时间
  requestsPerUserPerSecond: 5, // 增加请求频率测试内存缓存性能
  services: [], // 将在测试开始时创建
};

// 统计数据
const stats = {
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  totalResponseTime: 0,
  minResponseTime: Infinity,
  maxResponseTime: 0,
  errors: {},
};

/**
 * 创建测试服务
 */
async function createTestServices(count = 10) {
  console.log(`🔧 创建 ${count} 个测试服务...`);
  const services = [];
  
  for (let i = 0; i < count; i++) {
    try {
      const testService = {
        clientId: 10000 + i,
        client: `stress-test-${i}`,
        quota: JSON.stringify([{ traffic: 107374182400, speed: 2621440 }]),
        expiredAt: '2025-12-31',
        resetType: 1
      };

      const response = await axios.post(`${CONFIG.baseURL}/api/v1/services`, testService);
      services.push({
        id: response.data.id,
        clientId: testService.clientId,
        client: testService.client,
        username: response.data.username,
        password: response.data.password,
      });
    } catch (error) {
      console.error(`❌ 创建服务 ${i} 失败:`, error.message);
    }
  }
  
  console.log(`✅ 成功创建 ${services.length} 个测试服务`);
  return services;
}

/**
 * 模拟用户流量更新
 */
async function simulateTrafficUpdate(service) {
  const startTime = Date.now();
  
  try {
    const usageData = {
      username: service.username,
      password: service.password,
      upload: Math.floor(Math.random() * 1048576), // 0-1MB
      download: Math.floor(Math.random() * 2097152), // 0-2MB
    };
    
    await axios.put(`${CONFIG.baseURL}/api/v1/subscribes/${service.id}`, usageData);
    
    const responseTime = Date.now() - startTime;
    updateStats(true, responseTime);
    
    return { success: true, responseTime };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    updateStats(false, responseTime, error.message);
    
    return { success: false, error: error.message, responseTime };
  }
}

/**
 * 更新统计数据
 */
function updateStats(success, responseTime, error = null) {
  stats.totalRequests++;
  stats.totalResponseTime += responseTime;
  stats.minResponseTime = Math.min(stats.minResponseTime, responseTime);
  stats.maxResponseTime = Math.max(stats.maxResponseTime, responseTime);
  
  if (success) {
    stats.successfulRequests++;
  } else {
    stats.failedRequests++;
    if (error) {
      stats.errors[error] = (stats.errors[error] || 0) + 1;
    }
  }
}

/**
 * 工作进程逻辑
 */
async function workerProcess() {
  const workerId = cluster.worker.id;
  const services = CONFIG.services;
  
  if (services.length === 0) {
    console.error(`❌ Worker ${workerId}: 没有可用的测试服务`);
    return;
  }
  
  console.log(`🚀 Worker ${workerId} 开始压力测试...`);
  
  const endTime = Date.now() + (CONFIG.testDurationMinutes * 60 * 1000);
  const requestInterval = 1000 / CONFIG.requestsPerUserPerSecond;
  
  while (Date.now() < endTime) {
    const promises = [];
    
    // 每个工作进程同时处理多个服务
    for (let i = 0; i < Math.min(5, services.length); i++) {
      const service = services[Math.floor(Math.random() * services.length)];
      promises.push(simulateTrafficUpdate(service));
    }
    
    await Promise.all(promises);
    
    // 控制请求频率
    await new Promise(resolve => setTimeout(resolve, requestInterval));
  }
  
  console.log(`✅ Worker ${workerId} 完成测试`);
}

/**
 * 主进程逻辑
 */
async function masterProcess() {
  console.log('🎯 开始每日用量统计压力测试 (内存缓存版本)');
  console.log(`📊 配置: ${CONFIG.concurrentUsers} 并发用户, ${CONFIG.testDurationMinutes} 分钟, ${CONFIG.requestsPerUserPerSecond} 请求/秒/用户`);

  // 创建测试服务 (减少服务数量)
  CONFIG.services = await createTestServices(10);
  
  if (CONFIG.services.length === 0) {
    console.error('❌ 无法创建测试服务，退出测试');
    process.exit(1);
  }
  
  // 启动工作进程
  const numWorkers = Math.min(CONFIG.concurrentUsers, os.cpus().length);
  console.log(`🔄 启动 ${numWorkers} 个工作进程...`);
  
  for (let i = 0; i < numWorkers; i++) {
    const worker = cluster.fork();
    worker.send({ services: CONFIG.services });
  }
  
  // 监控工作进程
  cluster.on('exit', (worker, code, signal) => {
    console.log(`Worker ${worker.process.pid} 退出 (${signal || code})`);
  });
  
  // 定期输出统计信息
  const statsInterval = setInterval(() => {
    printStats();
  }, 10000); // 每10秒
  
  // 等待测试完成
  setTimeout(() => {
    console.log('\n🏁 压力测试完成');
    clearInterval(statsInterval);
    printFinalStats();
    
    // 清理测试服务
    cleanupTestServices();
    
    // 关闭所有工作进程
    for (const id in cluster.workers) {
      cluster.workers[id].kill();
    }
    
    process.exit(0);
  }, CONFIG.testDurationMinutes * 60 * 1000 + 5000);
}

/**
 * 打印统计信息
 */
function printStats() {
  const avgResponseTime = stats.totalRequests > 0 ? stats.totalResponseTime / stats.totalRequests : 0;
  const successRate = stats.totalRequests > 0 ? (stats.successfulRequests / stats.totalRequests * 100) : 0;
  
  console.log(`📈 实时统计:
    总请求: ${stats.totalRequests}
    成功: ${stats.successfulRequests} (${successRate.toFixed(2)}%)
    失败: ${stats.failedRequests}
    平均响应时间: ${avgResponseTime.toFixed(2)}ms
    最小响应时间: ${stats.minResponseTime === Infinity ? 0 : stats.minResponseTime}ms
    最大响应时间: ${stats.maxResponseTime}ms
  `);
}

/**
 * 打印最终统计信息
 */
function printFinalStats() {
  console.log('\n📊 最终测试结果:');
  printStats();
  
  if (Object.keys(stats.errors).length > 0) {
    console.log('\n❌ 错误统计:');
    for (const [error, count] of Object.entries(stats.errors)) {
      console.log(`  ${error}: ${count} 次`);
    }
  }
  
  // 性能评估
  const avgResponseTime = stats.totalRequests > 0 ? stats.totalResponseTime / stats.totalRequests : 0;
  const successRate = stats.totalRequests > 0 ? (stats.successfulRequests / stats.totalRequests * 100) : 0;
  
  console.log('\n🎯 性能评估:');
  if (avgResponseTime < 100) {
    console.log('✅ 响应时间: 优秀 (<100ms)');
  } else if (avgResponseTime < 500) {
    console.log('🟡 响应时间: 良好 (100-500ms)');
  } else {
    console.log('🔴 响应时间: 需要优化 (>500ms)');
  }
  
  if (successRate > 99) {
    console.log('✅ 成功率: 优秀 (>99%)');
  } else if (successRate > 95) {
    console.log('🟡 成功率: 良好 (95-99%)');
  } else {
    console.log('🔴 成功率: 需要优化 (<95%)');
  }
}

/**
 * 清理测试服务
 */
async function cleanupTestServices() {
  console.log('🧹 清理测试服务...');
  // 这里可以添加清理逻辑，比如删除测试数据
  // 由于这是测试环境，暂时跳过
}

// 主程序入口
if (cluster.isMaster) {
  masterProcess();
} else {
  // 接收主进程发送的服务列表
  process.on('message', (msg) => {
    if (msg.services) {
      CONFIG.services = msg.services;
      workerProcess();
    }
  });
}
