#!/usr/bin/env bash

wget https://api.crowdin.com/api/project/aquadata/download/zh-CN.zip?key=78f7886d30a972146fdb80e6536de69f -O /tmp/zh-CN.zip && unzip -d /tmp/zh-CN /tmp/zh-CN.zip && mv /tmp/zh-CN/en-US.json src/locale/locales/zh-Hans.json
wget https://api.crowdin.com/api/project/aquadata/download/zh-TW.zip?key=78f7886d30a972146fdb80e6536de69f -O /tmp/zh-TW.zip && unzip -d /tmp/zh-TW /tmp/zh-TW.zip && mv /tmp/zh-TW/en-US.json src/locale/locales/zh-Hant.json
#wget https://api.crowdin.com/api/project/aquadata/download/es-ES.zip?key=78f7886d30a972146fdb80e6536de69f -O /tmp/es-ES.zip && unzip -d /tmp /tmp/es-ES.zip && mv /tmp/messages.xlf src/locale/messages.es-ES.xlf
#wget https://api.crowdin.com/api/project/aquadata/download/ru.zip?key=78f7886d30a972146fdb80e6536de69f -O /tmp/ru.zip && unzip -d /tmp /tmp/ru.zip && mv /tmp/messages.xlf src/locale/messages.ru.xlf
#wget https://api.crowdin.com/api/project/aquadata/download/ja.zip?key=78f7886d30a972146fdb80e6536de69f -O /tmp/ja.zip && unzip -d /tmp /tmp/ja.zip && mv /tmp/messages.xlf src/locale/messages.ja.xlf

