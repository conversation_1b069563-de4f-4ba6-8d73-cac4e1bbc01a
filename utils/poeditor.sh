#!/usr/bin/env bash


# download key value json translations from https://poeditor.com
curl -X POST https://api.poeditor.com/v2/projects/export -d api_token="f0ef1e4affe2b90488b7e11de778132e" -d id="289989" -d language="en" -d type="key_value_json" | jq '.result.url' | xargs  wget -O  src/locales/en.json
curl -X POST https://api.poeditor.com/v2/projects/export -d api_token="f0ef1e4affe2b90488b7e11de778132e" -d id="289989" -d language="zh-hans" -d type="key_value_json" | jq '.result.url' | xargs  wget -O  src/locales/zh-Hans.json
curl -X POST https://api.poeditor.com/v2/projects/export -d api_token="f0ef1e4affe2b90488b7e11de778132e" -d id="289989" -d language="zh-hant" -d type="key_value_json" | jq '.result.url' | xargs  wget -O  src/locales/zh-Hant.json

