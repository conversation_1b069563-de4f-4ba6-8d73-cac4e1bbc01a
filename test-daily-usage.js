const axios = require('axios');

// 测试每日用量统计功能
async function testDailyUsage() {
  const baseURL = 'http://localhost:3000';
  
  // 测试数据
  const testService = {
    clientId: 12345,
    client: 'test-client',
    quota: JSON.stringify([{ traffic: 107374182400, speed: 2621440 }]),
    expiredAt: '2025-12-31',
    resetType: 1
  };

  try {
    console.log('🚀 开始测试每日用量统计功能...\n');

    // 1. 创建测试服务
    console.log('1. 创建测试服务...');
    const createResponse = await axios.post(`${baseURL}/api/v1/services`, testService);
    console.log('✅ 服务创建成功:', createResponse.data.id);
    const serviceId = createResponse.data.id;
    const username = createResponse.data.username;
    const password = createResponse.data.password;

    // 2. 模拟流量使用
    console.log('\n2. 模拟流量使用...');
    const usageData = {
      username: username,
      password: password,
      upload: 1048576,    // 1MB
      download: 2097152   // 2MB
    };
    
    const updateResponse = await axios.put(`${baseURL}/api/v1/subscribes/${serviceId}`, usageData);
    console.log('✅ 流量使用更新成功');

    // 等待一秒确保数据已保存
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 3. 查询每日用量统计
    console.log('\n3. 查询每日用量统计...');
    const dailyUsageQuery = {
      clientId: testService.clientId,
      client: testService.client
    };
    
    const dailyUsageResponse = await axios.post(`${baseURL}/api/v1/services/dailyUsage`, dailyUsageQuery);
    console.log('✅ 每日用量查询成功:');
    console.log(JSON.stringify(dailyUsageResponse.data, null, 2));

    // 4. 查询用量汇总
    console.log('\n4. 查询用量汇总...');
    const summaryResponse = await axios.post(`${baseURL}/api/v1/services/usageSummary`, dailyUsageQuery);
    console.log('✅ 用量汇总查询成功:');
    console.log(JSON.stringify(summaryResponse.data, null, 2));

    // 5. 测试服务重置
    console.log('\n5. 测试服务重置...');
    const resetData = {
      clientId: testService.clientId,
      client: testService.client
    };
    
    const resetResponse = await axios.post(`${baseURL}/api/v1/services/reset`, resetData);
    console.log('✅ 服务重置成功');

    // 6. 重置后再次查询每日用量
    console.log('\n6. 重置后查询每日用量...');
    const dailyUsageAfterReset = await axios.post(`${baseURL}/api/v1/services/dailyUsage`, dailyUsageQuery);
    console.log('✅ 重置后每日用量查询成功:');
    console.log(JSON.stringify(dailyUsageAfterReset.data, null, 2));

    // 7. 测试性能监控API
    console.log('\n7. 测试性能监控...');
    const performanceResponse = await axios.get(`${baseURL}/api/v1/services/performance`);
    console.log('✅ 性能监控查询成功:');
    console.log('当前指标:', JSON.stringify(performanceResponse.data.current, null, 2));
    if (performanceResponse.data.issues.length > 0) {
      console.log('⚠️ 性能问题:', performanceResponse.data.issues);
    } else {
      console.log('✅ 无性能问题');
    }

    console.log('\n🎉 所有测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

// 运行测试
if (require.main === module) {
  testDailyUsage();
}

module.exports = { testDailyUsage };
