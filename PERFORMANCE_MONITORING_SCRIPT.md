# 性能监控脚本使用指南

## 概述

为了提高安全性，我们将性能监控功能从API接口改为独立的Node.js脚本。这样可以避免通过HTTP接口暴露敏感的系统信息，同时提供更灵活的监控方式。

## 脚本位置

```
scripts/performance-monitor.js
```

## 使用方法

### 1. 单次性能报告

显示当前的系统和应用性能信息：

```bash
node scripts/performance-monitor.js
```

### 2. 持续监控模式

每5秒刷新一次性能信息（类似top命令）：

```bash
node scripts/performance-monitor.js --watch
```

或使用简写：

```bash
node scripts/performance-monitor.js -w
```

### 3. 自定义刷新间隔

设置自定义的刷新间隔（单位：秒）：

```bash
node scripts/performance-monitor.js --watch --interval=10
```

### 4. 查看帮助信息

```bash
node scripts/performance-monitor.js --help
```

## 监控内容

### 🖥️ 系统信息
- **平台信息**: 操作系统、架构
- **Node.js版本**: 当前运行的Node.js版本
- **CPU核心数**: 可用的CPU核心数量
- **系统运行时间**: 系统启动时间
- **负载平均值**: 1分钟、5分钟、15分钟负载

### 💾 内存信息
- **总内存**: 系统总内存容量
- **已用内存**: 当前已使用的内存
- **可用内存**: 当前可用的内存
- **内存使用率**: 内存使用百分比

### ⚙️ 进程信息
- **进程ID**: 当前应用进程ID
- **进程运行时间**: 应用启动时间
- **堆内存**: 总堆内存和已使用堆内存
- **RSS内存**: 常驻内存集大小
- **外部内存**: V8外部分配的内存

### 📊 应用统计
- **日志分析**: 分析应用日志文件
- **错误统计**: 最近的错误和警告数量
- **每日用量操作**: 用量统计相关操作数量
- **缓存操作**: 缓存相关操作统计

### ⚠️ 性能问题检测
自动检测并报告以下性能问题：
- 系统内存使用率 > 90%
- 堆内存使用率 > 80%
- CPU负载过高

## 示例输出

```
🔍 Performance Monitor Report
==================================================
📅 Generated at: 2025-01-11T12:00:00.000Z

🖥️  System Information
------------------------------
Platform: darwin arm64
Node.js: v18.17.0
CPU Cores: 8
System Uptime: 5d 12h 30m 45s
Load Average: 1.25, 1.18, 1.32

💾 Memory Information
------------------------------
Total: 16.00 GB
Used: 8.45 GB (52.81%)
Free: 7.55 GB

⚙️  Process Information
------------------------------
PID: 12345
Uptime: 2h 15m 30s
Heap Total: 256.00 MB
Heap Used: 128.50 MB (50.20%)
RSS: 312.45 MB
External: 45.23 MB

📊 Application Statistics
------------------------------
Total Log Lines: 15420
Recent Errors: 2
Recent Warnings: 8
Daily Usage Operations: 1250
Cache Operations: 3420

🗄️  Memory Cache Statistics
------------------------------
Total Entries: N/A - Connect to app instance
Memory Usage: N/A - Connect to app instance
Hit Rate: N/A - Connect to app instance

⚠️  Performance Issues
------------------------------
✅ No performance issues detected

==================================================
📝 Note: For real-time cache statistics, connect to the running application instance
```

## 监控模式特性

### 实时刷新
- 自动清屏并更新显示
- 可自定义刷新间隔
- 按Ctrl+C退出监控

### 颜色编码
- ✅ 绿色：正常状态
- 🟡 黄色：警告状态
- 🔴 红色：问题状态

## 安全优势

### 1. 无网络暴露
- 不通过HTTP接口暴露性能信息
- 避免潜在的信息泄露风险
- 只能通过服务器本地访问

### 2. 权限控制
- 需要服务器登录权限
- 可以通过文件权限控制访问
- 支持sudo权限管理

### 3. 审计友好
- 所有访问都有系统日志记录
- 可以追踪谁在什么时候查看了性能信息
- 符合安全审计要求

## 集成到运维流程

### 1. 定时检查
可以通过cron定时执行并保存报告：

```bash
# 每小时生成一次性能报告
0 * * * * /usr/bin/node /path/to/scripts/performance-monitor.js >> /var/log/performance.log 2>&1
```

### 2. 告警集成
结合其他监控工具使用：

```bash
#!/bin/bash
# 检查性能问题并发送告警
ISSUES=$(node scripts/performance-monitor.js | grep "🔴")
if [ ! -z "$ISSUES" ]; then
    echo "$ISSUES" | mail -s "Performance Alert" <EMAIL>
fi
```

### 3. 监控面板
可以将输出集成到监控面板或日志系统中。

## 故障排除

### 1. 权限问题
```bash
# 确保脚本有执行权限
chmod +x scripts/performance-monitor.js
```

### 2. 依赖问题
```bash
# 确保在项目根目录执行
cd /path/to/your/project
node scripts/performance-monitor.js
```

### 3. 日志文件访问
脚本会尝试读取 `logs/application.log` 文件，确保：
- 日志目录存在
- 有读取权限
- 日志文件格式正确

## 扩展功能

### 自定义监控指标
可以修改脚本添加更多监控指标：
- 数据库连接状态
- 外部API响应时间
- 自定义业务指标

### 输出格式
支持多种输出格式：
- 控制台彩色输出
- JSON格式输出
- CSV格式输出

## 总结

使用独立的性能监控脚本具有以下优势：

✅ **更安全** - 不通过网络暴露敏感信息
✅ **更灵活** - 支持多种使用模式
✅ **更详细** - 提供更丰富的系统信息
✅ **更易集成** - 容易集成到现有运维流程

这种方式既保证了安全性，又提供了强大的监控能力，是生产环境的最佳实践。
