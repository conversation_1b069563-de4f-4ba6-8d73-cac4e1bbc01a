#Suporapi Nodes API

## Installation

```bash
$ npm install
```

## Running the app

```bash
# development
$ npm run start

# watch mode
$ npm run start:dev

# incremental rebuild (webpack)
$ npm run webpack
$ npm run start:hmr

# production mode
$ npm run start:prod
```

## Attention
In order to start the service faster, we currently turn off migration.
If database id modified, please run `npm run typeorm migration:run` to update the database.

## Test

Start testing db:

```bash
 docker run  -p 3307:3306 -e MYSQL_DATABASE=superapi  -e MYSQL_USER=wb -e MYSQL_PASSWORD=test -e MYSQL_ROOT_PASSWORD=root  --detach --rm --name wb-mysql mysql:5.7
```

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```

## Conventions

- ormconfig.json is only used for typeorm cli in test and dev environment. Production is handled in database module, which receives database configurations form Config module.
so during test and dev environment, keep ormconfig and code in sync
