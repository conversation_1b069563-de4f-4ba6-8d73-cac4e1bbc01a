#!/usr/bin/env node

const os = require('os');
const fs = require('fs');
const path = require('path');

/**
 * 性能监控脚本
 * 用于显示应用的性能指标和系统状态
 */
class PerformanceMonitor {
  constructor() {
    this.startTime = Date.now();
  }

  /**
   * 获取系统信息
   */
  getSystemInfo() {
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const usedMem = totalMem - freeMem;
    
    return {
      platform: os.platform(),
      arch: os.arch(),
      nodeVersion: process.version,
      uptime: os.uptime(),
      loadAverage: os.loadavg(),
      cpuCount: os.cpus().length,
      memory: {
        total: this.formatBytes(totalMem),
        used: this.formatBytes(usedMem),
        free: this.formatBytes(freeMem),
        usagePercent: ((usedMem / totalMem) * 100).toFixed(2)
      }
    };
  }

  /**
   * 获取进程信息
   */
  getProcessInfo() {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    return {
      pid: process.pid,
      uptime: process.uptime(),
      memory: {
        rss: this.formatBytes(memUsage.rss),
        heapTotal: this.formatBytes(memUsage.heapTotal),
        heapUsed: this.formatBytes(memUsage.heapUsed),
        external: this.formatBytes(memUsage.external),
        heapUsagePercent: ((memUsage.heapUsed / memUsage.heapTotal) * 100).toFixed(2)
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system
      }
    };
  }

  /**
   * 获取应用统计信息（如果有日志文件）
   */
  async getAppStats() {
    try {
      // 尝试读取应用日志来获取统计信息
      const logPath = path.join(process.cwd(), 'logs', 'application.log');
      
      if (fs.existsSync(logPath)) {
        const logContent = fs.readFileSync(logPath, 'utf8');
        const lines = logContent.split('\n');
        
        // 分析最近的日志
        const recentLines = lines.slice(-1000); // 最近1000行
        
        const stats = {
          totalLogLines: lines.length,
          recentLogLines: recentLines.length,
          errorCount: recentLines.filter(line => line.includes('ERROR')).length,
          warnCount: recentLines.filter(line => line.includes('WARN')).length,
          dailyUsageOperations: recentLines.filter(line => line.includes('daily usage')).length,
          cacheOperations: recentLines.filter(line => line.includes('cache')).length,
        };
        
        return stats;
      }
    } catch (error) {
      return {
        error: 'Unable to read application logs',
        message: error.message
      };
    }
    
    return {
      message: 'No log file found'
    };
  }

  /**
   * 获取内存缓存统计（模拟）
   */
  getMemoryCacheStats() {
    // 这里可以通过读取应用的状态文件或其他方式获取缓存统计
    // 暂时返回模拟数据
    return {
      totalEntries: 'N/A - Connect to app instance',
      memoryUsage: 'N/A - Connect to app instance',
      hitRate: 'N/A - Connect to app instance',
      oldestEntry: 'N/A - Connect to app instance',
      newestEntry: 'N/A - Connect to app instance'
    };
  }

  /**
   * 检查性能问题
   */
  checkPerformanceIssues() {
    const issues = [];
    const systemInfo = this.getSystemInfo();
    const processInfo = this.getProcessInfo();
    
    // 检查系统内存使用
    if (parseFloat(systemInfo.memory.usagePercent) > 90) {
      issues.push(`High system memory usage: ${systemInfo.memory.usagePercent}%`);
    }
    
    // 检查进程内存使用
    if (parseFloat(processInfo.memory.heapUsagePercent) > 80) {
      issues.push(`High heap memory usage: ${processInfo.memory.heapUsagePercent}%`);
    }
    
    // 检查负载平均值
    const loadAvg = systemInfo.loadAverage[0];
    if (loadAvg > systemInfo.cpuCount * 0.8) {
      issues.push(`High CPU load: ${loadAvg.toFixed(2)} (${systemInfo.cpuCount} cores)`);
    }
    
    return issues;
  }

  /**
   * 格式化字节数
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 格式化时间
   */
  formatUptime(seconds) {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    return `${days}d ${hours}h ${minutes}m ${secs}s`;
  }

  /**
   * 显示性能报告
   */
  async displayReport() {
    console.log('\n🔍 Performance Monitor Report');
    console.log('=' .repeat(50));
    console.log(`📅 Generated at: ${new Date().toISOString()}`);
    
    // 系统信息
    console.log('\n🖥️  System Information');
    console.log('-'.repeat(30));
    const systemInfo = this.getSystemInfo();
    console.log(`Platform: ${systemInfo.platform} ${systemInfo.arch}`);
    console.log(`Node.js: ${systemInfo.nodeVersion}`);
    console.log(`CPU Cores: ${systemInfo.cpuCount}`);
    console.log(`System Uptime: ${this.formatUptime(systemInfo.uptime)}`);
    console.log(`Load Average: ${systemInfo.loadAverage.map(l => l.toFixed(2)).join(', ')}`);
    
    // 内存信息
    console.log('\n💾 Memory Information');
    console.log('-'.repeat(30));
    console.log(`Total: ${systemInfo.memory.total}`);
    console.log(`Used: ${systemInfo.memory.used} (${systemInfo.memory.usagePercent}%)`);
    console.log(`Free: ${systemInfo.memory.free}`);
    
    // 进程信息
    console.log('\n⚙️  Process Information');
    console.log('-'.repeat(30));
    const processInfo = this.getProcessInfo();
    console.log(`PID: ${processInfo.pid}`);
    console.log(`Uptime: ${this.formatUptime(processInfo.uptime)}`);
    console.log(`Heap Total: ${processInfo.memory.heapTotal}`);
    console.log(`Heap Used: ${processInfo.memory.heapUsed} (${processInfo.memory.heapUsagePercent}%)`);
    console.log(`RSS: ${processInfo.memory.rss}`);
    console.log(`External: ${processInfo.memory.external}`);
    
    // 应用统计
    console.log('\n📊 Application Statistics');
    console.log('-'.repeat(30));
    const appStats = await this.getAppStats();
    if (appStats.error) {
      console.log(`❌ ${appStats.error}: ${appStats.message}`);
    } else if (appStats.message) {
      console.log(`ℹ️  ${appStats.message}`);
    } else {
      console.log(`Total Log Lines: ${appStats.totalLogLines}`);
      console.log(`Recent Errors: ${appStats.errorCount}`);
      console.log(`Recent Warnings: ${appStats.warnCount}`);
      console.log(`Daily Usage Operations: ${appStats.dailyUsageOperations}`);
      console.log(`Cache Operations: ${appStats.cacheOperations}`);
    }
    
    // 内存缓存统计
    console.log('\n🗄️  Memory Cache Statistics');
    console.log('-'.repeat(30));
    const cacheStats = this.getMemoryCacheStats();
    console.log(`Total Entries: ${cacheStats.totalEntries}`);
    console.log(`Memory Usage: ${cacheStats.memoryUsage}`);
    console.log(`Hit Rate: ${cacheStats.hitRate}`);
    
    // 性能问题检查
    console.log('\n⚠️  Performance Issues');
    console.log('-'.repeat(30));
    const issues = this.checkPerformanceIssues();
    if (issues.length === 0) {
      console.log('✅ No performance issues detected');
    } else {
      issues.forEach(issue => {
        console.log(`🔴 ${issue}`);
      });
    }
    
    console.log('\n' + '='.repeat(50));
    console.log('📝 Note: For real-time cache statistics, connect to the running application instance');
    console.log('');
  }

  /**
   * 监控模式 - 持续显示性能信息
   */
  async startMonitoring(interval = 5000) {
    console.log(`🔄 Starting performance monitoring (refresh every ${interval/1000}s)`);
    console.log('Press Ctrl+C to stop\n');
    
    const monitor = async () => {
      // 清屏
      console.clear();
      await this.displayReport();
    };
    
    // 立即显示一次
    await monitor();
    
    // 设置定时器
    const intervalId = setInterval(monitor, interval);
    
    // 处理退出信号
    process.on('SIGINT', () => {
      clearInterval(intervalId);
      console.log('\n👋 Performance monitoring stopped');
      process.exit(0);
    });
  }
}

// 命令行参数处理
const args = process.argv.slice(2);
const monitor = new PerformanceMonitor();

if (args.includes('--watch') || args.includes('-w')) {
  // 监控模式
  const intervalArg = args.find(arg => arg.startsWith('--interval='));
  const interval = intervalArg ? parseInt(intervalArg.split('=')[1]) * 1000 : 5000;
  monitor.startMonitoring(interval);
} else if (args.includes('--help') || args.includes('-h')) {
  // 帮助信息
  console.log(`
📊 Performance Monitor Script

Usage:
  node scripts/performance-monitor.js [options]

Options:
  --watch, -w              Start monitoring mode (continuous updates)
  --interval=<seconds>     Set refresh interval for watch mode (default: 5)
  --help, -h              Show this help message

Examples:
  node scripts/performance-monitor.js                    # Show single report
  node scripts/performance-monitor.js --watch            # Start monitoring
  node scripts/performance-monitor.js -w --interval=10   # Monitor with 10s interval
`);
} else {
  // 单次报告
  monitor.displayReport();
}
