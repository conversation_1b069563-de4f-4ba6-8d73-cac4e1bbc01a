#!/usr/bin/env node

const axios = require('axios');

/**
 * 性能问题诊断脚本
 * 帮助识别压力测试中成功率低的原因
 */

const CONFIG = {
  baseURL: 'http://localhost:3001',
  testService: {
    clientId: Math.floor(Math.random() * 1000),
    client: 'rubiz'
  }
};

const diagnostics = {
  serverConnection: false,
  serviceCreation: false,
  trafficUpdate: false,
  usageQuery: false,
  concurrentRequests: false,
  memoryPressure: false,
  errors: []
};

/**
 * 测试服务器连接
 */
async function testServerConnection() {
  console.log('🔍 1. 测试服务器连接...');
  try {
    const response = await axios.get(`${CONFIG.baseURL}/api/v1/services`, { timeout: 5000 });
    diagnostics.serverConnection = true;
    console.log('✅ 服务器连接正常');
    return true;
  } catch (error) {
    diagnostics.serverConnection = false;
    diagnostics.errors.push(`服务器连接失败: ${error.message}`);
    console.log('❌ 服务器连接失败:', error.message);
    return false;
  }
}

/**
 * 测试服务创建
 */
async function testServiceCreation() {
  console.log('\n🔍 2. 测试服务创建...');
  try {
    const serviceData = {
      clientId: CONFIG.testService.clientId,
      client: CONFIG.testService.client,
      product: 'rubiz',
      username: `test_${Date.now()}`,
      password: 'test123',
      upload: 0,
      download: 0,
      total: 0,
      expiryTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      status: 'Active'
    };

    const response = await axios.post(`${CONFIG.baseURL}/api/v1/services`, serviceData, { timeout: 10000 });
    CONFIG.testService.id = response.data.id;
    CONFIG.testService.username = response.data.username;
    CONFIG.testService.password = response.data.password;
    
    diagnostics.serviceCreation = true;
    console.log('✅ 服务创建成功, ID:', response.data.id);
    return true;
  } catch (error) {
    diagnostics.serviceCreation = false;
    diagnostics.errors.push(`服务创建失败: ${error.message}`);
    console.log('❌ 服务创建失败:', error.message);
    if (error.response) {
      console.log('响应状态:', error.response.status);
      console.log('响应数据:', error.response.data);
    }
    return false;
  }
}

/**
 * 测试流量更新
 */
async function testTrafficUpdate() {
  console.log('\n🔍 3. 测试流量更新...');
  try {
    const usageData = {
      username: CONFIG.testService.username,
      password: CONFIG.testService.password,
      upload: 1048576, // 1MB
      download: 2097152, // 2MB
    };

    const startTime = Date.now();
    const response = await axios.put(`${CONFIG.baseURL}/api/v1/subscribes/${CONFIG.testService.id}`, usageData, { timeout: 10000 });
    const responseTime = Date.now() - startTime;
    
    diagnostics.trafficUpdate = true;
    console.log('✅ 流量更新成功, 响应时间:', responseTime + 'ms');
    return true;
  } catch (error) {
    diagnostics.trafficUpdate = false;
    diagnostics.errors.push(`流量更新失败: ${error.message}`);
    console.log('❌ 流量更新失败:', error.message);
    if (error.response) {
      console.log('响应状态:', error.response.status);
      console.log('响应数据:', error.response.data);
    }
    return false;
  }
}

/**
 * 测试用量查询
 */
async function testUsageQuery() {
  console.log('\n🔍 4. 测试用量查询...');
  try {
    const queryData = {
      clientId: CONFIG.testService.clientId,
      client: CONFIG.testService.client
    };

    const startTime = Date.now();
    const response = await axios.post(`${CONFIG.baseURL}/api/v1/services/dailyUsage`, queryData, { timeout: 10000 });
    const responseTime = Date.now() - startTime;
    
    diagnostics.usageQuery = true;
    console.log('✅ 用量查询成功, 响应时间:', responseTime + 'ms');
    console.log('查询结果:', response.data.length, '条记录');
    return true;
  } catch (error) {
    diagnostics.usageQuery = false;
    diagnostics.errors.push(`用量查询失败: ${error.message}`);
    console.log('❌ 用量查询失败:', error.message);
    if (error.response) {
      console.log('响应状态:', error.response.status);
      console.log('响应数据:', error.response.data);
    }
    return false;
  }
}

/**
 * 测试并发请求
 */
async function testConcurrentRequests() {
  console.log('\n🔍 5. 测试并发请求处理...');
  try {
    const concurrentLevel = 10;
    const promises = [];
    
    for (let i = 0; i < concurrentLevel; i++) {
      const usageData = {
        username: CONFIG.testService.username,
        password: CONFIG.testService.password,
        upload: Math.floor(Math.random() * 1048576),
        download: Math.floor(Math.random() * 2097152),
      };
      
      promises.push(
        axios.put(`${CONFIG.baseURL}/api/v1/subscribes/${CONFIG.testService.id}`, usageData, { timeout: 15000 })
          .then(() => ({ success: true }))
          .catch(error => ({ success: false, error: error.message }))
      );
    }
    
    const results = await Promise.all(promises);
    const successCount = results.filter(r => r.success).length;
    const successRate = (successCount / concurrentLevel) * 100;
    
    if (successRate >= 90) {
      diagnostics.concurrentRequests = true;
      console.log(`✅ 并发请求测试通过: ${successCount}/${concurrentLevel} (${successRate.toFixed(1)}%)`);
    } else {
      diagnostics.concurrentRequests = false;
      diagnostics.errors.push(`并发请求成功率低: ${successRate.toFixed(1)}%`);
      console.log(`❌ 并发请求成功率低: ${successCount}/${concurrentLevel} (${successRate.toFixed(1)}%)`);
      
      // 显示失败的错误
      const failures = results.filter(r => !r.success);
      const errorCounts = {};
      failures.forEach(f => {
        errorCounts[f.error] = (errorCounts[f.error] || 0) + 1;
      });
      
      console.log('失败原因统计:');
      Object.entries(errorCounts).forEach(([error, count]) => {
        console.log(`  ${error}: ${count} 次`);
      });
    }
    
    return successRate >= 90;
  } catch (error) {
    diagnostics.concurrentRequests = false;
    diagnostics.errors.push(`并发测试异常: ${error.message}`);
    console.log('❌ 并发测试异常:', error.message);
    return false;
  }
}

/**
 * 检查内存压力
 */
async function checkMemoryPressure() {
  console.log('\n🔍 6. 检查内存压力...');
  try {
    // 连续发送多个请求，观察响应时间变化
    const requestCount = 50;
    const responseTimes = [];
    
    console.log(`发送 ${requestCount} 个连续请求...`);
    
    for (let i = 0; i < requestCount; i++) {
      const usageData = {
        username: CONFIG.testService.username,
        password: CONFIG.testService.password,
        upload: Math.floor(Math.random() * 1048576),
        download: Math.floor(Math.random() * 2097152),
      };
      
      const startTime = Date.now();
      try {
        await axios.put(`${CONFIG.baseURL}/api/v1/subscribes/${CONFIG.testService.id}`, usageData, { timeout: 10000 });
        const responseTime = Date.now() - startTime;
        responseTimes.push(responseTime);
      } catch (error) {
        responseTimes.push(-1); // 标记失败
      }
      
      // 小延迟避免过快请求
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    
    const successfulTimes = responseTimes.filter(t => t > 0);
    const failureCount = responseTimes.filter(t => t === -1).length;
    const avgResponseTime = successfulTimes.length > 0 ? 
      successfulTimes.reduce((a, b) => a + b, 0) / successfulTimes.length : 0;
    
    const successRate = ((requestCount - failureCount) / requestCount) * 100;
    
    console.log(`成功率: ${successRate.toFixed(1)}% (${requestCount - failureCount}/${requestCount})`);
    console.log(`平均响应时间: ${avgResponseTime.toFixed(1)}ms`);
    
    if (successRate >= 95 && avgResponseTime < 500) {
      diagnostics.memoryPressure = true;
      console.log('✅ 内存压力测试通过');
    } else {
      diagnostics.memoryPressure = false;
      diagnostics.errors.push(`内存压力测试失败: 成功率${successRate.toFixed(1)}%, 平均响应时间${avgResponseTime.toFixed(1)}ms`);
      console.log('❌ 内存压力测试失败');
    }
    
    return successRate >= 95;
  } catch (error) {
    diagnostics.memoryPressure = false;
    diagnostics.errors.push(`内存压力测试异常: ${error.message}`);
    console.log('❌ 内存压力测试异常:', error.message);
    return false;
  }
}

/**
 * 清理测试服务
 */
async function cleanup() {
  if (CONFIG.testService.id) {
    try {
      await axios.delete(`${CONFIG.baseURL}/api/v1/services/${CONFIG.testService.id}`);
      console.log('🧹 测试服务已清理');
    } catch (error) {
      console.log('⚠️ 清理测试服务失败:', error.message);
    }
  }
}

/**
 * 生成诊断报告
 */
function generateReport() {
  console.log('\n📋 诊断报告');
  console.log('='.repeat(50));
  
  const tests = [
    { name: '服务器连接', status: diagnostics.serverConnection },
    { name: '服务创建', status: diagnostics.serviceCreation },
    { name: '流量更新', status: diagnostics.trafficUpdate },
    { name: '用量查询', status: diagnostics.usageQuery },
    { name: '并发请求', status: diagnostics.concurrentRequests },
    { name: '内存压力', status: diagnostics.memoryPressure },
  ];
  
  tests.forEach(test => {
    const icon = test.status ? '✅' : '❌';
    console.log(`${icon} ${test.name}: ${test.status ? '通过' : '失败'}`);
  });
  
  if (diagnostics.errors.length > 0) {
    console.log('\n❌ 发现的问题:');
    diagnostics.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error}`);
    });
  }
  
  console.log('\n💡 建议:');
  
  if (!diagnostics.serverConnection) {
    console.log('- 检查服务器是否在端口3001上运行');
    console.log('- 检查防火墙设置');
  }
  
  if (!diagnostics.serviceCreation) {
    console.log('- 检查数据库连接');
    console.log('- 检查服务创建API的参数验证');
  }
  
  if (!diagnostics.trafficUpdate) {
    console.log('- 检查流量更新API的认证逻辑');
    console.log('- 检查用户名密码验证');
  }
  
  if (!diagnostics.concurrentRequests) {
    console.log('- 考虑增加数据库连接池大小');
    console.log('- 检查是否有死锁或长时间运行的查询');
    console.log('- 考虑优化内存缓存的并发处理');
  }
  
  if (!diagnostics.memoryPressure) {
    console.log('- 检查内存缓存的大小限制');
    console.log('- 考虑调整批量同步频率');
    console.log('- 监控应用内存使用情况');
  }
  
  const passedTests = tests.filter(t => t.status).length;
  const totalTests = tests.length;
  
  console.log(`\n🎯 总体评估: ${passedTests}/${totalTests} 项测试通过`);
  
  if (passedTests === totalTests) {
    console.log('✅ 所有测试通过，系统运行正常');
  } else if (passedTests >= totalTests * 0.8) {
    console.log('🟡 大部分测试通过，存在一些性能问题');
  } else {
    console.log('🔴 多项测试失败，需要重点关注系统稳定性');
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🔍 开始性能问题诊断...\n');
  
  try {
    // 按顺序执行诊断测试
    await testServerConnection();
    await testServiceCreation();
    await testTrafficUpdate();
    await testUsageQuery();
    await testConcurrentRequests();
    await checkMemoryPressure();
    
  } catch (error) {
    console.error('诊断过程中发生异常:', error.message);
  } finally {
    await cleanup();
    generateReport();
  }
}

// 运行诊断
main().catch(console.error);
