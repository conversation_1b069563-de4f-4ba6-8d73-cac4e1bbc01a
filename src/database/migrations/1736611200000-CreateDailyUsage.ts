import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateDailyUsage1736611200000 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`
            CREATE TABLE \`daily_usage\` (
                \`id\` int(11) NOT NULL AUTO_INCREMENT,
                \`client_id\` int(10) NOT NULL,
                \`client\` varchar(32) NOT NULL,
                \`date\` date NOT NULL,
                \`upload\` bigint(20) NOT NULL DEFAULT '0',
                \`download\` bigint(20) NOT NULL DEFAULT '0',
                \`total\` bigint(20) NOT NULL DEFAULT '0',
                \`created_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                \`updated_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (\`id\`),
                UNIQUE KEY \`IDX_daily_usage_client_date\` (\`client_id\`, \`client\`, \`date\`),
                <PERSON>E<PERSON> \`IDX_daily_usage_client_id\` (\`client_id\`),
                KEY \`IDX_daily_usage_client\` (\`client\`),
                KEY \`IDX_daily_usage_date\` (\`date\`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query('DROP TABLE `daily_usage`');
    }
}
