import {MigrationInterface, QueryRunner} from 'typeorm';

export class AddExpiredAt1590674994225 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query('ALTER TABLE `services` DROP COLUMN `addon_traffic_quota`');
        await queryRunner.query('ALTER TABLE `services` DROP COLUMN `addon_traffic_upload`');
        await queryRunner.query('ALTER TABLE `services` DROP COLUMN `addon_traffic_download`');
        await queryRunner.query('ALTER TABLE `services` DROP COLUMN `addon_traffic_last_updated`');
        await queryRunner.query('ALTER TABLE `services` DROP COLUMN `addon_traffic_expiry`');
        await queryRunner.query('ALTER TABLE `services` DROP COLUMN `remind`');

        await queryRunner.query('ALTER TABLE `services` ADD `expired_at` datetime NOT NULL DEFAULT \'1970-01-01 00:00:00\'');

        await queryRunner.query('ALTER TABLE `services` DROP COLUMN `last_updated_time`');
        await queryRunner.query('ALTER TABLE `services` ADD `last_updated_time` datetime NULL DEFAULT \'1970-01-01 00:00:00\'');

        await queryRunner.query('ALTER TABLE `services` MODIFY `traffic_next_reset_date` datetime NOT NULL DEFAULT \'1970-01-01 00:00:00\'');
    }

    public async down(queryRunner: QueryRunner): Promise<any> {

        await queryRunner.query('ALTER TABLE `services` DROP COLUMN `traffic_reset_date`');

        await queryRunner.query('ALTER TABLE `services` DROP COLUMN `last_updated_time`');
        await queryRunner.query('ALTER TABLE `services` ADD `last_updated_time` int(10) NULL');

        await queryRunner.query('ALTER TABLE `services` DROP COLUMN `expired_at`');

        await queryRunner.query('ALTER TABLE `services` ADD `remind` varchar(255) NOT NULL DEFAULT \'[]\'');
        await queryRunner.query('ALTER TABLE `services` ADD `addon_traffic_expiry` bigint NOT NULL DEFAULT \'0\'');
        await queryRunner.query('ALTER TABLE `services` ADD `addon_traffic_last_updated` bigint NOT NULL DEFAULT \'0\'');
        await queryRunner.query('ALTER TABLE `services` ADD `addon_traffic_download` bigint NOT NULL DEFAULT \'0\'');
        await queryRunner.query('ALTER TABLE `services` ADD `addon_traffic_upload` bigint NOT NULL DEFAULT \'0\'');
        await queryRunner.query('ALTER TABLE `services` ADD `addon_traffic_quota` bigint NOT NULL DEFAULT \'0\'');
    }

}
