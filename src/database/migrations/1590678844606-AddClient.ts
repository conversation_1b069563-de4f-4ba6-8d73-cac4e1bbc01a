import {MigrationInterface, QueryRunner} from 'typeorm';

export class AddClient1590678844606 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query('ALTER TABLE `services` ADD `client` varchar(255) NOT NULL');
        await queryRunner.query('ALTER TABLE `services` CHANGE `whmcs_service_id` `client_id` int(10) NOT NULL');
        await queryRunner.query('CREATE UNIQUE INDEX `IDX_03d855e3f0fe4194ce6ae2898e` ON `services` (`client`, `client_id`)');
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query('DROP INDEX `IDX_03d855e3f0fe4194ce6ae2898e` ON `services`');
        await queryRunner.query('ALTER TABLE `services` CHANGE `client_id` `whmcs_service_id` int(10) NOT NULL');
        await queryRunner.query('ALTER TABLE `services` DROP COLUMN `client`');
    }

}
