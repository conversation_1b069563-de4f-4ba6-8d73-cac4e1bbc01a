import {MigrationInterface, QueryRunner} from 'typeorm';

export class CreateSubscribe1550198178384 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        queryRunner.query('CREATE TABLE `services` (`id` int NOT NULL AUTO_INCREMENT, `whmcs_service_id` int(10) NOT NULL, `uuid` varchar(255) NOT NULL, `username` varchar(255) NOT NULL DEFAULT 0, `password` varchar(255) NOT NULL, `last_updated_time` int(10) NULL DEFAULT null, `traffic_quota` text NOT NULL, `traffic_upload` bigint(20) NOT NULL DEFAULT 0, `traffic_download` bigint(20) NOT NULL DEFAULT 0, `traffic_reset_cycle` tinyint(4) NOT NULL DEFAULT 0, `traffic_next_reset_date` varchar(255) NOT NULL DEFAULT \'2099-09-09\', `addon_traffic_quota` bigint(20) NOT NULL DEFAULT 0, `addon_traffic_upload` bigint(20) NOT NULL DEFAULT 0, `addon_traffic_download` bigint(20) NOT NULL DEFAULT 0, `addon_traffic_last_updated` bigint(20) NOT NULL DEFAULT 0, `addon_traffic_expiry` bigint(20) NOT NULL DEFAULT 0, `connection_limit` int(10) NULL DEFAULT 1, `status` tinyint(4) NOT NULL DEFAULT 0, `remind` varchar(255) NOT NULL DEFAULT \'[]\', PRIMARY KEY (`id`)) ENGINE=InnoDB')
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        queryRunner.dropTable('user');
    }

}
