import {MigrationInterface, QueryRunner} from 'typeorm';

export class AddUUID1612609991005 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query('ALTER TABLE `services` ADD `uuid` varchar(255) NOT NULL');
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query('ALTER TABLE `services` DROP COLUMN `uuid`');
    }

}
