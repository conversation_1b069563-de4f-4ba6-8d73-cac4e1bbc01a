import {MigrationInterface, QueryRunner} from 'typeorm';

export class AddUniqueKey1590677779014 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query('ALTER TABLE `services` DROP COLUMN `uuid`');
        await queryRunner.query('update `services` set `expired_at` = `traffic_next_reset_date` where `expired_at` is not null');
    }

    public async down(queryRunner: QueryRunner): Promise<any> {

    }

}
