import {MigrationInterface, QueryRunner} from 'typeorm';

export class AddUniqueToUUID1612955081122 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query('ALTER TABLE `services` ADD UNIQUE INDEX `IDX_fb19bb0ecb2f6581d02f263547` (`uuid`)');
    }

    public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query('ALTER TABLE `services` DROP INDEX `IDX_fb19bb0ecb2f6581d02f263547`');
    }

}
