import { Injectable, MiddlewareFunction, NestMiddleware } from '@nestjs/common';

@Injectable()
export class RobotMiddleware implements NestMiddleware {
  resolve(...args: any[]): MiddlewareFunction {
    return (req, res, next) => {
      if (/robots.txt/.test(req.baseUrl)) {
        next();
      } else {
        const source = req.headers['user-agent'];
        const ua = [
          /spider/i,
          /Spider/i,
          /Googlebot/i,
          /Bingbot/i,
          /Yahoo! Slurp/i,
        ];
        const match = ua.some(u => u.test(source));
        if (match) {
          res.status(403).send('Go away, robot.');
        } else {
          next();
        }
      }
    };
  }
}
