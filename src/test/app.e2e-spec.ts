import { HttpStatus, INestApplication, ValidationPipe } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import * as request from 'supertest';
import { AppModule } from '../app.module';
import { JweService } from '../auth/jwe/jwe.service';
import * as i18n from 'i18n';
import * as moment from 'moment';
import * as sinon from 'sinon';
import * as YAML from 'yaml';
import { NodesService } from '../nodes/nodes.service';
import { stringify } from 'querystring';
import { ServiceService } from '../service/service.service';
import * as crypto from 'crypto';
import * as agent from 'superagent';
// import * as fakeNodes from "./fakeNodes"
// import * as fakePods from "./fakePods"
import * as path from "path";

describe('AppController (e2e)', () => {

  jest.setTimeout(200000);
  let clock;
  let app: INestApplication;
  let jweService: JweService;
  let nodesService: NodesService;
  let serviceService: ServiceService;
  beforeAll(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({
      transform: true,
      whitelist: true,
      disableErrorMessages: true,
    }));
    i18n.configure({
      locales: ['en', 'zh-Hans', 'zh-Hant'],
      queryParameter: 'language',
      directory: path.resolve( __dirname , '../locale/locales')
    });
    app.use(i18n.init);
    await app.init();
    jweService = app.get(JweService);
    nodesService = app.get(NodesService);
    serviceService = app.get(ServiceService);
  });

  beforeEach( () => {
    clock = sinon.useFakeTimers({
      now: 1634227200000, // 2021-10-15 00:00:00
    });
  });

  afterEach( () => {
    clock.restore();
  });

  // default quota
  const quota = [
    {
      traffic: 53687091200,
      speed: 0,
    },
  ];

  describe('Services', () => {
    // case 1
    it('create trail service', async () => {
      clock.restore();
      const expiredAt = moment.utc().add(3, 'days');
      const clientId = ran();
      const res = await request(app.getHttpServer())
          .post('/api/v1/services')
          .set('Accept', 'application/json')
          .send({client: 'jetstream', clientId, quota: JSON.stringify(quota), expiredAt: expiredAt.toDate()});

      const service = res.body;
      expect(service.clientId).toEqual(clientId);
      expect(service.resetDate).toContain(expiredAt.format('YYYY-MM-DD')); // 2021-10-15
      expect(service.status).toEqual(0);
    });

    it('create normal service (1 month)', async () => {
      const clientId = ran();
      const res = await request(app.getHttpServer())
          .post('/api/v1/services')
          .set('Accept', 'application/json')
          .send({client: 'jetstream', clientId, quota: JSON.stringify(quota), expiredAt: moment.utc().add(1, 'months').toDate()}); // 2021-11-15

      const service = res.body;
      expect(service.clientId).toEqual(clientId);
      expect(service.resetDate).toContain(moment.utc().add(1, 'months').format('YYYY-MM-DD'));
      expect(service.status).toEqual(0);
    });

    it('create normal service (3 month)', async () => {
      const clientId = ran();
      const res = await request(app.getHttpServer())
          .post('/api/v1/services')
          .set('Accept', 'application/json')
          .send({client: 'jetstream', clientId, quota: JSON.stringify(quota), expiredAt: moment.utc().add(3, 'months').toDate()}); // 2021-11-15

      const service = res.body;
      expect(service.clientId).toEqual(clientId);
      expect(service.resetDate).toContain(moment.utc().add(1, 'months').format('YYYY-MM-DD'));
      expect(service.status).toEqual(0);
    });

    // case 2
    it('create trail service， then renew for 1 month', async () => {
      clock.restore();
      const expiredAt = moment.utc().add(3, 'days');
      const clientId = ran();
      const res = await request(app.getHttpServer())
          .post('/api/v1/services')
          .set('Accept', 'application/json')
          .send({client: 'jetstream', clientId, quota: JSON.stringify(quota), expiredAt: expiredAt.toDate()});

      let service = res.body;
      expect(service.clientId).toEqual(clientId);
      expect(service.resetDate).toContain(expiredAt.format('YYYY-MM-DD')); // 2021-10-15
      expect(service.status).toEqual(0);

      const resOfUpdate = await  request(app.getHttpServer())
          .post(`/api/v1/services/update`)
          .set('Accept', 'application/json')
          .send({client: 'jetstream', clientId, quota: JSON.stringify(quota), status: 1, expiredAt: moment.utc().add(1, 'month').format()});
      service = resOfUpdate.body;
      expect(service.status).toEqual(1);
      expect(service.resetDate).toContain(moment().utc().add(1, 'month').format('YYYY-MM-DD'));
    });

    // case 3
    it('create trail service， then renew for 3 month', async () => {
      const expiredAt = moment.utc().add(3, 'days');
      const clientId = ran();
      const res = await request(app.getHttpServer())
          .post('/api/v1/services')
          .set('Accept', 'application/json')
          .send({client: 'jetstream', clientId, quota: JSON.stringify(quota), expiredAt: expiredAt.toDate()});

      let service = res.body;
      expect(service.clientId).toEqual(clientId);
      expect(service.resetDate).toContain(expiredAt.format('YYYY-MM-DD')); // 2021-10-15
      expect(service.status).toEqual(0);

      // update its expiration to 1 month to activate it
      const resOfUpdate = await  request(app.getHttpServer())
          .post(`/api/v1/services/update`)
          .set('Accept', 'application/json')
          .send({client: 'jetstream', clientId, quota: JSON.stringify(quota), status: 1, expiredAt: moment.utc().add(3, 'days').add(3, 'month').format()});
      service = resOfUpdate.body;
      expect(service.status).toEqual(1);
      expect(service.resetDate).toContain(moment().utc().add(3, 'days').format('YYYY-MM-DD'));
    });

    // case 4
    it('create a service renew for 3 month, suspend 1 month, then renew 3 month', async () => {
      sinon.useFakeTimers({
        now: 1621008000000, // 2021-05-15 00:00:00
      });
      const expiredAt = moment.utc().add(3, 'month');
      const clientId = ran();
      const res = await request(app.getHttpServer())
          .post('/api/v1/services')
          .set('Accept', 'application/json')
          .send({client: 'jetstream', clientId, quota: JSON.stringify(quota), expiredAt: expiredAt.toDate()});

      let service = res.body;
      expect(service.clientId).toEqual(clientId);
      expect(service.resetDate).toContain(moment.utc().add(1, 'month').format('YYYY-MM-DD')); // 2021-10-15
      expect(service.status).toEqual(0);

      sinon.useFakeTimers({
        now: 1623700800000, // 2021-06-15 00:00:00
      });
      let resOfUpdate = await  request(app.getHttpServer())
          .post(`/api/v1/services/update`)
          .set('Accept', 'application/json')
          .send({client: 'jetstream', clientId, quota: JSON.stringify(quota), status: 1, expiredAt: expiredAt.toDate()});
      service = resOfUpdate.body;
      expect(service.status).toEqual(1);
      expect(service.resetDate).toContain(moment().utc().add(1, 'month').format('YYYY-MM-DD'));

      sinon.useFakeTimers({
        now: 1629403200000, // 2021-08-20 04:00:00
      });
      resOfUpdate = await  request(app.getHttpServer())
          .post(`/api/v1/services/update`)
          .set('Accept', 'application/json')
          .send({client: 'jetstream', clientId, quota: JSON.stringify(quota), expiredAt: expiredAt.toDate()});
      service = resOfUpdate.body;
      expect(service.status).toEqual(1);
      expect(service.resetDate).toContain(expiredAt.format('YYYY-MM-DD'));

      sinon.useFakeTimers({
        now: 1631649600000, // 2021-09-15 04:00:00
      });

      resOfUpdate = await  request(app.getHttpServer())
          .post(`/api/v1/services/update`)
          .set('Accept', 'application/json')
          .send({client: 'jetstream', clientId, quota: JSON.stringify(quota), status: 1, expiredAt: moment.utc().add(3, 'month').format()});
      service = resOfUpdate.body;
      expect(service.status).toEqual(1);
      expect(service.resetDate).toContain(moment().utc().add(1, 'month').format('YYYY-MM-DD'));
    });

    it('manage the life cycle of a service', async () => {
      clock.restore();
      const clientId = ran();
      // create a new
      const res = await request(app.getHttpServer())
          .post('/api/v1/services')
          .set('Accept', 'application/json')
          .send({client: 'jetstream', clientId, quota: JSON.stringify(quota), expiredAt: moment.utc().toDate()}); // 2021-10-15

      let service = res.body;
      expect(service.clientId).toBeDefined();
      // coz create service need some times, so we just compare date(YYYY-MM-DD)
      expect(service.resetDate).toContain(moment.utc().format('YYYY-MM-DD')); // 2021-10-15
      expect(service.status).toEqual(0);

      // update its expiration to 1 month to activate it
      let resOfUpdate = await  request(app.getHttpServer())
          .post(`/api/v1/services/update`)
          .set('Accept', 'application/json')
          .send({client: 'jetstream', clientId, quota: JSON.stringify(quota), status: 1, expiredAt: moment.utc().add(1, 'month').format()});
      service = resOfUpdate.body;
      expect(service.status).toEqual(1);
      expect(service.resetDate).toContain(moment().utc().add(1, 'month').format('YYYY-MM-DD'));

      // simulate suspension
      resOfUpdate = await  request(app.getHttpServer())
          .post(`/api/v1/services/update`)
          .set('Accept', 'application/json')
          .send({client: 'jetstream', clientId: res.body.clientId, status: 0});
      service = resOfUpdate.body;
      expect(service.status).toEqual(0);
    });

    it('reset data', async () => {
      clock.restore();
      const s = serviceService.newService();
      s.client = 'jetstream';
      s.clientId = ran();
      s.trafficDownload = 10000;
      s.trafficQuota = JSON.stringify(quota);
      s.expiredAt = moment.utc().add(3, 'month').toDate();
      await s.save();
      // expire date is today, coz expire has not passed
      expect(s.trafficDownload).toEqual('10000');
      expect(moment.utc(s.trafficNextResetDate).format('YYYY-MM-DD')).toEqual(moment.utc().format('YYYY-MM-DD'));
      // reset
      await serviceService.resetData();
      await s.reload();
      expect(s.trafficDownload).toEqual('0');
      expect(moment.utc(s.trafficNextResetDate).format('YYYY-MM-DD')).toEqual(moment.utc().add(1, 'month').format('YYYY-MM-DD'));
    });

    it('reset data with manual (15 days)', async () => {
      clock.restore();
      const s = serviceService.newService();
      s.client = 'jetstream';
      s.clientId = ran();
      s.trafficDownload = 10000;
      s.trafficQuota = JSON.stringify(quota);
      s.expiredAt = moment.utc().add(15, 'days').toDate();
      await s.save();
      expect(s.trafficDownload).toEqual('10000');

      s.reset();
      await s.save();
      await s.reload();
      expect(s.trafficDownload).toEqual('0');
      expect(moment.utc(s.trafficNextResetDate).format('YYYY-MM-DD')).toEqual(moment().utc().add(15, 'days').format('YYYY-MM-DD'));
    });

    it('reset data with manual (3 month)', async () => {
      clock.restore();
      const s = serviceService.newService();
      s.client = 'jetstream';
      s.clientId = ran();
      s.trafficDownload = 10000;
      s.trafficQuota = JSON.stringify(quota);
      s.expiredAt = moment.utc().add(3, 'month').toDate();
      await s.save();
      expect(s.trafficDownload).toEqual('10000');

      s.reset();
      await s.save();
      await s.reload();
      expect(s.trafficDownload).toEqual('0');
      expect(moment.utc(s.trafficNextResetDate).format('YYYY-MM-DD')).toEqual(moment().utc().add(1, 'month').format('YYYY-MM-DD'));
    });

    it ('upload usage', async () => {
      // create a new
      const res = await  request(app.getHttpServer())
          .post('/api/v1/services')
          .set('Accept', 'application/json')
          .send({client: 'jetstream', clientId: ran(), quota: JSON.stringify(quota), expiredAt: moment.utc().format()});

      let service = res.body;

      // upload
      const resOfUpload = await  request(app.getHttpServer())
          .put(`/api/v1/subscribes/${service.id}`)
          .set('Accept', 'application/json')
          .send({username: service.username, password: service.password, upload: 10000, download: 20000});

      service = resOfUpload.body;
      expect(service.updatedAt).toBeDefined();
    });

    it ('reduce usage', async () => {
      // create a new
      const res = await  request(app.getHttpServer())
          .post('/api/v1/services')
          .set('Accept', 'application/json')
          .send({client: 'jetstream', clientId: ran(), quota: JSON.stringify(quota), expiredAt: moment.utc().format()});

      let service = res.body;

      // upload
      const resOfUpload = await  request(app.getHttpServer())
          .put(`/api/v1/subscribes/${service.id}`)
          .set('Accept', 'application/json')
          .send({username: service.username, password: service.password, upload: 10000, download: 20000});

      service = resOfUpload.body;
      expect(service.updatedAt).toBeDefined();

      // upload
      const resOfReduce = await  request(app.getHttpServer())
          .post(`/api/v1/services/jetstream/${service.clientId}/reduceUsage`)
          .set('Accept', 'application/json')
          .send({client: 'jetstream', clientId: service.clientId, delta: 100000});

      service = resOfReduce.body;
      expect(service.upload).toEqual('0');
      expect(service.download).toEqual('0');
    });

    it ('has a subscription', async () => {
      // sinon.stub(nodesService.k8sApi, 'listNode').callsFake(()=>{
      //   return fakeNodes;
      // })
      // sinon.stub(nodesService.k8sApi, 'listNamespacedPod').callsFake(()=>{
      //   return fakePods;
      // })

      // wait 3 seconds for k8s api
      // await new Promise((resolve) => setTimeout(resolve, 3000));
      // create a new
      const clientId = ran();
      const res = await  request(app.getHttpServer())
          .post('/api/v1/services')
          .set('Accept', 'application/json')
          .send({client: 'jetstream', clientId, quota: JSON.stringify(quota), expiredAt: moment.utc().format()});
      expect(res.status).toEqual(201);

      await testSubscription(app, clientId, 'yes');
      await testSubscription(app, clientId, 'no');
    });

  });

  describe('Nodes', () => {

    it('get all nodes', async () => {

      const resOfNodes = await  request(app.getHttpServer())
          .get(`/api/v1/nodes/jetstream`)
          .set('Accept', 'application/json');
      expect(resOfNodes.status).toEqual(200);
      expect(resOfNodes.body).toBeDefined();
    });
  });
});

function ran() {
  return Math.floor((Math.random() * 1000000) + 1);
}

function token(password) {
  return crypto.createHash('md5').update(password).digest('hex');
}

async function testSubscription(app, clientId, direct) {
  let resOfSub = await  request(app.getHttpServer())
      .get(`/api/v1/services/jetstream/${clientId}/subscriptionV2?direct=${direct}&agent=none`)
      .set('Accept', 'application/json');
  expect(resOfSub.status).toEqual(200);
  const noneProxy = JSON.parse(resOfSub.text);
  expect(noneProxy.length).toBeGreaterThan(0);
  expect(noneProxy[0].type).toEqual('https');
  expect(noneProxy[0].probe).toEqual('http://releases.ubuntu.com');
  expect(noneProxy[0].username).toBeDefined();
  expect(noneProxy[0].password).toBeDefined();
  expect(resOfSub.headers['content-type']).toEqual('application/json; charset=utf-8');

  resOfSub = await  request(app.getHttpServer())
      .get(`/api/v1/services/jetstream/${clientId}/subscriptionV2?direct=${direct}&agent=clash`)
      .set('Accept', 'application/json');
  expect(resOfSub.status).toEqual(200);
  const clashProxy = YAML.parse(resOfSub.text);
  expect(clashProxy.proxies.length).toBeGreaterThan(2);
  expect(clashProxy.proxies[0].username).toBeDefined();
  expect(clashProxy.proxies[0].password).toBeDefined();
  expect(clashProxy.proxies[0].server).toBeDefined();
  expect(clashProxy.proxies[2].server).toBeDefined();
  expect(clashProxy.proxies[2].port).toBeDefined();
  expect(clashProxy.proxies[2].type).toBeDefined();
  expect(resOfSub.headers['content-type']).toEqual('text/x-yaml; charset=utf-8');

  resOfSub = await  request(app.getHttpServer())
      .get(`/api/v1/services/jetstream/${clientId}/subscriptionV2?direct=${direct}&agent=switchyomega`)
      .set('Accept', 'application/json');
  expect(resOfSub.status).toEqual(200);
  expect(resOfSub.text).toBeDefined();
  expect(resOfSub.headers['content-type']).toEqual('application/json; charset=utf-8');

  resOfSub = await  request(app.getHttpServer())
      .get(`/api/v1/services/jetstream/${clientId}/subscriptionV2?direct=${direct}&agent=shadowrocket`)
      .set('Accept', 'application/json');
  expect(resOfSub.status).toEqual(200);
  expect(resOfSub.text).toBeDefined();
  expect(resOfSub.headers['content-type']).toEqual('text/plain; charset=utf-8');

  resOfSub = await  request(app.getHttpServer())
      .get(`/api/v1/services/jetstream/${clientId}/subscriptionV2?direct=${direct}&agent=quantumult`)
      .set('Accept', 'application/json');
  expect(resOfSub.status).toEqual(200);
  expect(resOfSub.text).toBeDefined();
  expect(resOfSub.headers['content-type']).toEqual('text/plain; charset=utf-8');

  resOfSub = await  request(app.getHttpServer())
      .get(`/api/v1/services/jetstream/${clientId}/subscriptionV2?direct=${direct}&agent=quantumultx`)
      .set('Accept', 'application/json');
  expect(resOfSub.status).toEqual(200);
  expect(resOfSub.text).toBeDefined();
  expect(resOfSub.headers['content-type']).toEqual('text/plain; charset=utf-8');

  resOfSub = await  request(app.getHttpServer())
      .get(`/api/v1/services/jetstream/${clientId}/subscriptionV2?direct=${direct}&agent=v2rayu`)
      .set('Accept', 'application/json');
  expect(resOfSub.status).toEqual(200);
  expect(resOfSub.text).toBeDefined();
  expect(resOfSub.headers['content-type']).toEqual('text/plain; charset=utf-8');

  resOfSub = await  request(app.getHttpServer())
      .get(`/api/v1/services/jetstream/${clientId}/subscriptionV2?direct=${direct}&agent=jetstream`)
      .set('Accept', 'application/json');
  expect(resOfSub.status).toEqual(200);
  const jetstreamProxy = YAML.parse(resOfSub.text);
  expect(jetstreamProxy.proxies.length).toBeGreaterThan(0);
  expect(jetstreamProxy.proxies[0].username).toBeDefined();
  expect(jetstreamProxy.proxies[0].password).toBeDefined();
  expect(jetstreamProxy.proxies[0].type).toBeDefined();
  expect(jetstreamProxy.proxies[0].server).toBeDefined();
  expect(resOfSub.headers['content-type']).toEqual('text/x-yaml; charset=utf-8');

  resOfSub = await  request(app.getHttpServer())
      .get(`/api/v1/services/jetstream/${clientId}/subscriptionV2?direct=${direct}&agent=jetstream&allShards=yes`)
      .set('Accept', 'application/json');
  expect(resOfSub.status).toEqual(200);
  const jetstreamAllShardsProxy = YAML.parse(resOfSub.text);
  expect(jetstreamAllShardsProxy.proxies.length).toBeGreaterThan(0);
  expect(jetstreamAllShardsProxy.proxies.length).toBeGreaterThanOrEqual(jetstreamProxy.proxies.length);
  expect(jetstreamAllShardsProxy.proxies[0].username).toBeDefined();
  expect(jetstreamAllShardsProxy.proxies[0].password).toBeDefined();
  expect(jetstreamAllShardsProxy.proxies[0].type).toBeDefined();
  expect(jetstreamAllShardsProxy.proxies[0].server).toBeDefined();
  expect(resOfSub.headers['content-type']).toEqual('text/x-yaml; charset=utf-8');

  resOfSub = await  request(app.getHttpServer())
      .get(`/api/v1/services/jetstream/${clientId}/subscriptionV2?direct=${direct}&agent=leaf&allShards=yes`)
      .set('Accept', 'application/json');
  expect(resOfSub.status).toEqual(200);
  expect(/trojan/.test(resOfSub.text)).toBeDefined();
  expect(resOfSub.headers['content-type']).toEqual('text/plain; charset=utf-8');

  resOfSub = await  request(app.getHttpServer())
      .get(`/api/v1/services/jetstream/${clientId}/subscriptionV2?direct=${direct}&agent=leaf&allShards=yes`)
      .set('Accept', 'application/json');
  expect(resOfSub.status).toEqual(200);
  expect(/trojan/.test(resOfSub.text)).toBeDefined();
  expect(resOfSub.headers['content-type']).toEqual('text/plain; charset=utf-8');

  resOfSub = await  request(app.getHttpServer())
      .get(`/api/v1/services/jetstream/${clientId}/subscriptionV2?direct=${direct}&agent=leaf&binary=yes&allShards=yes`)
      .set('Accept', 'application/json');
  expect(resOfSub.status).toEqual(200);
  expect(/trojan/.test(resOfSub.text)).toBeDefined();
  expect(resOfSub.text.includes('')).toEqual(true);
  expect(resOfSub.headers['content-type']).toEqual('text/plain; charset=utf-8');
}

async function testSubscriptionV2(app, clientId, direct) {

  const resOfSubV2 = await  request(app.getHttpServer())
      .get(`/api/v1/services/jetstream/${clientId}/subscriptionV2?direct=${direct}&agent=jetstream&allShards=yes`)
      .set('Accept', 'application/json');
  expect(resOfSubV2.status).toEqual(200);
  const jetstreamAllShardsProxyV2 = YAML.parse(resOfSubV2.text);
  expect(jetstreamAllShardsProxyV2.proxies.length).toBeGreaterThan(0);
  expect(jetstreamAllShardsProxyV2.proxies.length).toBeGreaterThanOrEqual(jetstreamAllShardsProxyV2.proxies.length);
  expect(jetstreamAllShardsProxyV2.proxies[0].username).toBeDefined();
  expect(jetstreamAllShardsProxyV2.proxies[0].password).toBeDefined();
  expect(jetstreamAllShardsProxyV2.proxies[0].type).toBeDefined();
  expect(jetstreamAllShardsProxyV2.proxies[0].server).toBeDefined();
  expect(resOfSubV2.headers['content-type']).toEqual('text/x-yaml; charset=utf-8');
}
