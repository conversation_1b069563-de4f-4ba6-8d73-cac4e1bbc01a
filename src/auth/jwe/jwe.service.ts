import { Injectable, Logger } from '@nestjs/common';
import {JWE, JW<PERSON>} from 'node-jose';
/**
 *  With the help of https://codeburst.io/securing-tokens-with-help-from-jose-33d8c31835a1
 */
@Injectable()
export class JweService {

  private readonly logger = new Logger(JweService.name);
  private privateKey: any;
  private publicKey: any;

  // default ttl is 1 month 30 * 24 * 60 * 60 * 1000
  constructor( private rawPrivateKey: any, private rawPublicKey: any, private ttl = 2592000000) {}

  /*
    data: arbitrary data to embed in sub filed in
    ttl: this may override default ttl
    return JWE token
   */
  async encrypt(data, ttl = null) {
    if (!data) {  throw new Error('Missing raw data.'); }
    const raw = {
      iss: 'superapi',
      exp: Date.now() + (ttl ? ttl : this.ttl),
      sub: data,
    };
    if (this.publicKey == null) {
      this.publicKey =  await JWK.asKey(this.rawPublicKey, 'pem');
    }

    const buffer = Buffer.from(JSON.stringify(raw));
    const encrypted = await JWE.createEncrypt({ format: 'compact' }, this.publicKey)
      .update(buffer)
      .final();

    return Buffer.from(JSON.stringify(encrypted)).toString('base64');

  }

  async decrypt( encrypted) {
    if (!encrypted) { return null; }
    if (this.privateKey == null) {
      this.privateKey =  await JWK.asKey(this.rawPrivateKey, 'pem');
    }

    try {

      const baseDecoded = Buffer.from(encrypted, 'base64').toString();
      const decoded = JSON.parse(baseDecoded);
      const { payload } = await JWE.createDecrypt(this.privateKey).decrypt(decoded);

      const raw = JSON.parse(payload);
      if (raw.exp < Date.now()) {
        this.logger.error(`token is expired`);
        return null;
      }
      return raw.sub;

    } catch (e) {
      this.logger.error(`failed to decrypt token due to ${e}`);
      return null;
    }

  }
}
