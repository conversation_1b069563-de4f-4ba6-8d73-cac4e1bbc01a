import { Test, TestingModule } from '@nestjs/testing';
import { JweService } from './jwe.service';
import { readFileSync } from 'fs';
import * as path from 'path';

describe('JweService', () => {
  let service: JweService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ {
        provide: JweService,
        useValue: new JweService(readFileSync(path.resolve(__filename, '../keys/key.pem')), readFileSync(path.resolve(__filename, '../keys/cert.pem'))),
      }],
    }).compile();
    service = module.get<JweService>(JweService);
  });
  it('should be defined', async () => {
    expect(service).toBeDefined();
    const raw = {
      id: 1,
    };

    const encrypted = await service.encrypt(raw);
    const decrypted = await service.decrypt(encrypted);

    expect(encrypted).toBeDefined();
    expect(decrypted).toEqual(raw);
  });
});
