import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { JweService } from './jwe/jwe.service';
import { readFileSync } from 'fs';
import * as path from 'path';
import { AuthGuard } from './auth.guard';

@Module({
  providers: [AuthService, {
    provide: JweService,
    useValue: new JweService(readFileSync(path.resolve(__filename, '../../../keys/key.pem')), readFileSync(path.resolve(__filename, '../../../keys/cert.pem'))),
  }],

  exports: [JweService, AuthService],
})
export class AuthModule {}
