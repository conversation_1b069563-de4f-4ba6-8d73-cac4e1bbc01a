import { CanActivate, ExecutionContext, HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { Observable } from 'rxjs';
import { JweService } from './jwe/jwe.service';

@Injectable()
export class AuthGuard implements CanActivate {

  constructor(private jwe: JweService) {}
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();

    if (request.headers.authorization !== null) {
      const token = request.headers.authorization.split(' ')[1];

      return this.jwe.decrypt(token)
        .then(payloay => {
          if (payloay == null) {return false; }
          request.user  = payloay;
          return true;
        });
    } else {
      throw new HttpException({
        status: HttpStatus.FORBIDDEN,
        error: 'CredentialIsWrong',
        message: 'Credential is not correct',
      }, 403);
    }
  }
}
