const fs = require('fs');
const path = require('path')
const YAML = require('yaml')
const Firestore = require('@google-cloud/firestore')

const firestore = new Firestore({
    projectId: 'aquadata-efa8e',
    keyFilename: path.resolve(__filename, '../../../firebase.json'),
});

const file = fs.readFileSync(path.resolve(__filename, '../../../templates/config.yml'), 'utf8');
const clashFile = YAML.parse(file);


async function uploadRules() {
    const batch = firestore.batch();
    let rules = []

    clashFile.rules.forEach((rule, index) => {
        rules.push({
            index,
            rule
        })
    });

    const ruleRef = firestore.collection('uat-rules').doc('test');
    batch.set(ruleRef, { rules });

    await batch.commit();
    console.log('Rules uploaded successfully');
}

uploadRules().catch(console.error);
