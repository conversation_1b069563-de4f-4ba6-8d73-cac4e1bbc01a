import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { AppController } from './app.controller';
import { ConfigModule } from './config/config.module';
import { NotificationModule } from './notification/notification.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigService } from './config/config.service';
import { Service } from './service/service.entity';
import { NodesModule } from './nodes/nodes.module';
import { AuthModule } from './auth/auth.module';
import { CommonModule } from './common/common.module';
import { LocaleModule } from './locale/locale.module';
import { ServiceModule } from './service/service.module';
import { RobotMiddleware } from './robot/robot.middleware';
import { ProviderModule } from './provider/provider.module';

const config = new ConfigService(`envs/${process.env.NODE_ENV}.env`);

@Module({
  imports: [
    TypeOrmModule.forRoot({
      name: 'default',
      type: 'mysql',
      host: config.get('DB_HOST'),
      port: Number(config.get('DB_PORT')),
      username: config.get('DB_USER'),
      password: config.get('DB_PASS'),
      database: config.get('DB_NAME'),
      entities: [
        Service,
      ],
      synchronize: false,
  }),
    ConfigModule, NotificationModule, NodesModule, AuthModule, CommonModule, LocaleModule, ServiceModule, ProviderModule,
  ],
  controllers: [AppController],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(RobotMiddleware)
      .forRoutes('*');
  }
}
