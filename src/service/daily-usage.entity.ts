import { Entity, Column, PrimaryGeneratedColumn, BaseEntity, Index } from 'typeorm';
import * as moment from 'moment';

@Entity('daily_usage')
@Index(['clientId', 'client', 'date'], { unique: true })
export class DailyUsage extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'int', width: 10, name: 'client_id', nullable: false })
  clientId: number;

  @Column({ type: 'varchar', width: 32, name: 'client', nullable: false })
  client: string;

  @Column({ type: 'date', name: 'date', nullable: false })
  date: Date;

  @Column({ type: 'bigint', width: 20, name: 'upload', nullable: false, default: 0 })
  upload: number;

  @Column({ type: 'bigint', width: 20, name: 'download', nullable: false, default: 0 })
  download: number;

  @Column({ type: 'bigint', width: 20, name: 'total', nullable: false, default: 0 })
  total: number;

  @Column({ type: 'datetime', name: 'created_at', nullable: false, default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ type: 'datetime', name: 'updated_at', nullable: false, default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  toJson() {
    return {
      id: this.id,
      clientId: this.clientId,
      client: this.client,
      date: moment.utc(this.date).format('YYYY-MM-DD'),
      upload: `${this.upload}`,
      download: `${this.download}`,
      total: `${this.total}`,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }

  /**
   * 更新用量数据
   * @param uploadDelta 上传增量
   * @param downloadDelta 下载增量
   */
  updateUsage(uploadDelta: number, downloadDelta: number) {
    this.upload += uploadDelta;
    this.download += downloadDelta;
    this.total = this.upload + this.download;
  }

  /**
   * 重置用量数据（用于服务重置时）
   */
  reset() {
    this.upload = 0;
    this.download = 0;
    this.total = 0;
  }
}
