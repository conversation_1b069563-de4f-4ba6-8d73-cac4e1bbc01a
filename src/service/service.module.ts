import { Module } from '@nestjs/common';
import { ConfigModule } from '../config/config.module';
import { AuthModule } from '../auth/auth.module';
import { CommonModule } from '../common/common.module';
import { LocaleModule } from '../locale/locale.module';
import { ServicesController } from './services.controller';
import { ServiceService } from './service.service';
import { ProxyService } from './proxy.service';
import { UsageStatsModule } from '../usage-stats/usage-stats.module';
import { NodesModule } from '../nodes/nodes.module';
import { NodesService } from '../nodes/nodes.service';

@Module({
  imports: [ConfigModule, AuthModule, CommonModule, LocaleModule, NodesModule, UsageStatsModule],
  controllers: [ServicesController],
  providers: [ServiceService, ProxyService, NodesService],
})
export class ServiceModule {}
