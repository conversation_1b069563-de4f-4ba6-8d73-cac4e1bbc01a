import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '../config/config.module';
import { AuthModule } from '../auth/auth.module';
import { CommonModule } from '../common/common.module';
import { LocaleModule } from '../locale/locale.module';
import { ServicesController } from './services.controller';
import { ServiceService } from './service.service';
import { ProxyService } from './proxy.service';
import { DailyUsageService } from './daily-usage.service';
import { PerformanceMonitorService } from './performance-monitor.service';
import { NodesModule } from '../nodes/nodes.module';
import { NodesService } from '../nodes/nodes.service';

@Module({
  imports: [ConfigModule, AuthModule, CommonModule, LocaleModule, NodesModule],
  controllers: [ServicesController],
  providers: [ServiceService, ProxyService, NodesService, DailyUsageService, PerformanceMonitorService],
})
export class ServiceModule {}
