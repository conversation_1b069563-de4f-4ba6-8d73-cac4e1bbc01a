import { HttpException, HttpStatus, Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Service } from './service.entity';
import * as crypto from 'crypto';
import * as uuid from 'uuid';
import * as schedule from 'node-schedule';
import * as moment from 'moment';
import { getRepository, MoreThan } from 'typeorm';
import { Firestore } from '@google-cloud/firestore';
import * as path from 'path';
import { ConfigService } from '../config/config.service';
import { CommonService } from '../common/common.service';
import { Response } from 'express';

@Injectable()
export class ServiceService implements OnModuleInit {

  private readonly logger = new Logger(ServiceService.name);
  private firestore: Firestore;
  constructor(
    private config: ConfigService,
    private commonService: CommonService,
  ) {
    // reset data daily each day
    const useExternalRest = this.config.get('USE_EXTERNAL_RESET');
    if (!useExternalRest) {
      this.logger.log('Reset service using the integrated reset service');
      schedule.scheduleJob('0 0 4 * * *', async () => {
        await this.resetData();
      });
    } else {
      this.logger.log('Reset service using the external reset service');
    }
    this.firestore = new Firestore({
      projectId: 'aquadata-efa8e',
      keyFilename: path.resolve(__filename, '../../../firebase.json'),
    });
  }

  static randomString(): string {
    return crypto.randomBytes(5).toString('hex');
  }

  newService(): Service {
    const s = new Service();
    s.username = ServiceService.randomString();
    s.password = ServiceService.randomString();
    s.uuid = uuid.v4();

    s.expiredAt = moment.utc().toDate();
    s.trafficNextResetDate = ServiceService.getNextResetDate(moment.utc().format('YYYY-MM-DD'));
    return  s;
  }

  resetPassword(s: Service): Service {
    s.username = ServiceService.randomString();
    s.password = ServiceService.randomString();
    s.uuid = uuid.v4();
    return s;
  }

  /**
   * Reset data of services
   */
  public async resetData() {
    const services = await getRepository(Service).createQueryBuilder('services')
      .where('date(expired_at) >= CURDATE() and date(traffic_next_reset_date) = CURDATE() AND AND traffic_reset_cycle = 1')
      .getMany();
    for (const s of services) {
      s.reset();
      await s.save();
      this.logger.log(`reset data for service ${s.id}`);
    }
  }

  /**
   * Sync data to nodes
   * @param service
   */
  async syncServices(service: Service) {
    if (this.commonService.isDev()) {
      return;
    }
    const doc = this.firestore.collection(this.config.get('SYNC_CHANNEL'));
    await doc.doc(`${service.id}`).set(service.toJson());
    this.logger.log(`Service: finish sync services ${JSON.stringify({username: service.username})}`);
  }

  static getNextResetDate(expDate: string): Date {
    const today = moment.utc();
    const expireDate = moment.utc(expDate);
    // For trial, if expire date is less than a month (including February in non-leap years), return expiry date as the next reset date
    if (expireDate.diff(today, 'days') < moment.utc().daysInMonth()) {
      return expireDate.toDate();
    }
    // For normal
    let resetDate = moment.utc().date(this.calcDay(today.get('month'), expireDate.get('date')));
    if (resetDate.diff(today, 'days') <= 0) {
      resetDate = moment.utc()
        .date(this.calcDay(moment.utc().get('month') + 1, expireDate.get('date')))
        .add(1, 'month');
    }
    return resetDate.startOf('day').toDate();
  }

  private static calcDay(month: number, day: number): number {
    if (day > 28) {
      // Ensure that the maximum reset date in February will only be 28th or 29th
      if (month === 1) {
        if (moment.utc().isLeapYear()) {
          return 29;
        }
        return 28;
      }
      // Ensure that the maximum reset date of the small month (Apr June Sept Nov) is 30th
      if (day === 31) {
        if ([0, 2, 4, 6, 7, 9, 11].includes(month)) {
          return 31;
        }
        return 30;
      }
    }
    return day;
  }

  async getService(clientId: string, client: string) {
    const services = await Service.find({ where: { clientId, client} });
    if (services.length === 0) {
      throw new HttpException({
        error: 'NoSuchService',
        message: 'We do not have this service',
      }, HttpStatus.NOT_FOUND);
    }
    return services.pop();
  }

  setMIMEAndHeader(res: Response, agent: string): void {
    const json = ['none', 'switchyomega'];
    const yaml = ['clash', 'jetstream'];
    if (json.includes(agent)) {
      res.append('Content-Type', 'application/json');
    } else if (yaml.includes(agent)) {
      res.append('Content-Type', 'text/x-yaml; charset=utf-8');
    } else {
      res.append('Content-Type', 'text/plain; charset=utf-8');
    }
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate'); // HTTP 1.1.
    res.setHeader('Pragma', 'no-cache'); // HTTP 1.0.
    res.setHeader('Expires', '0'); // Proxies.
    res.setHeader('status', '200'); // Proxies.
    res.setHeader('vary', 'Accept-Encoding');
    res.setHeader('Date', new Date().toString());
  }

  getProtocolsByProduct(product: string) {
    let types = ['https', 'v2ray', 'trojan', 'ss'];
    const productSupportedProtocols = this.config.get('PRODUCT_SUPPORTED_PROTOCOLS');
    if (productSupportedProtocols) {
      productSupportedProtocols.split(';').map(item => {
        const [productName, protocols] = item.split(':');
        if (product.match(productName)) {
          types = protocols.split(',');
        }
      });
    }
    return types;
  }

  getNodePrefix(nodeName: string): string {
    return nodeName.split('-')[0];
  }

  sortNodes(nodes, productSupportedProtocols: string[]) {
    const protocolOrder = this.config.get('PROTOCOL_ORDER').split(',');
    const countryOrder = this.config.get('COUNTRY_ORDER').split(',');
    const nodeOrder = this.config.get('NODE_ORDER').split(',');

    // Get all the countries and protocols in the node, sort them in ascending alphabetical order and insert them into our custom sorted array
    const allProtocols = [];
    const allCountries = [];
    nodes.forEach(node => {
      if (!protocolOrder.includes(node.type) && !allProtocols.includes(node.type)) {
        allProtocols.push(node.type);
      }
      if (!countryOrder.includes(node.country) && !allCountries.includes(node.country)) {
        allCountries.push(node.country);
      }
    });
    allProtocols.sort();
    allCountries.sort();
    protocolOrder.push(...allProtocols);
    countryOrder.push(...allCountries);

    const allNodes = [];

    protocolOrder.forEach(protocol => {
      // only supported protocols be added to node list
      if (!productSupportedProtocols.includes(protocol)) {
        return;
      }
      const allSingleProtocolNodes = nodes.filter(node => node.type === protocol);
      countryOrder.forEach(country => {
        const singleCountryNodes = allSingleProtocolNodes.filter(node => node.country === country);
        singleCountryNodes.sort((a, b) => {
          return nodeOrder.indexOf(this.getNodePrefix(a.name)) - nodeOrder.indexOf(this.getNodePrefix(b.name));
        });
        allNodes.push(...singleCountryNodes);
      });
    });
    return allNodes;
  }

  async onModuleInit() {
    // sync service for v2ray
    // const yesterday = moment.utc().add(-1, 'day');
    // const allActives = await Service.find({ where: { expiredAt: MoreThan(yesterday.toDate()) } });
    // for (const service of allActives) {
    //   this.syncServices(service);
    // }
  }
}
