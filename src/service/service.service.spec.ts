import { Test, TestingModule } from '@nestjs/testing';
import { ServiceService } from './service.service';

describe('ServiceService', () => {
  let service: ServiceService;
  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ServiceService],
    }).compile();
    service = module.get<ServiceService>(ServiceService);
  });
  it('should be defined', () => {
    const s = service.newService();
    expect(s.username).toBeDefined();
  });
});
