import {HttpException, HttpStatus, Injectable, Logger, OnModuleInit} from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import * as YAML from 'yaml';
import Handlebars from 'handlebars';
import * as lodash from 'lodash';
import {Service} from './service.entity';
import {LocaleService} from '../locale/locale.service';
import * as moment from 'moment';
import * as base64url from 'base64url';
import * as os from 'os';
import * as crypto from 'crypto';
import {NodesService} from '../nodes/nodes.service';
import {Firestore} from '@google-cloud/firestore';
import { json } from 'express';

@Injectable()
export class ProxyService implements OnModuleInit {
  private readonly logger = new Logger(ProxyService.name);
  private readonly clashTemplate;
  private readonly groupingTemplate;
  private readonly fallbackTemplate;
  private readonly jetstreamTemplate;
  private readonly leafTemplate;
  private readonly surfboardTemplate;
  private readonly surgeFooterTemplate;
  private readonly surgeHeaderTemplate;
  private firestore: Firestore;
  private env = process.env.NODE_ENV;
  private rules = new Map();

  constructor(
      private localeService: LocaleService,
      private nodesService: NodesService,
  ) {
    this.firestore = new Firestore({
      projectId: 'aquadata-efa8e',
      keyFilename: path.resolve(__filename, '../../../firebase.json'),
    });

    const file = fs.readFileSync(path.resolve(__filename, '../../../templates/config.yml'), 'utf8');
    const groupingFile = fs.readFileSync(path.resolve(__filename, '../../../templates/grouping.yml'), 'utf8');
    const jetstreamFile = fs.readFileSync(path.resolve(__filename, '../../../templates/jetstream.yml'), 'utf8');
    const fallbackFile = fs.readFileSync(path.resolve(__filename, '../../../templates/fallback.yml'), 'utf8');

    this.leafTemplate = fs.readFileSync(path.resolve(__filename, '../../../templates/leaf.conf'), 'utf8');
    this.surfboardTemplate = fs.readFileSync(path.resolve(__filename, '../../../templates/surfboard.conf'), 'utf8');
    this.surgeFooterTemplate = fs.readFileSync(path.resolve(__filename, '../../../templates/config-surge-footer.text'), 'utf8');
    this.surgeHeaderTemplate = fs.readFileSync(path.resolve(__filename, '../../../templates/config-surge-header.text'), 'utf8');

    this.jetstreamTemplate = YAML.parse(jetstreamFile);
    this.clashTemplate = YAML.parse(file);
    this.groupingTemplate = YAML.parse(groupingFile);
    this.fallbackTemplate = YAML.parse(fallbackFile);
  }

  onModuleInit(): any {
    this.firestore.collection(this.env === 'prod' ? 'rules' : 'uat-rules').onSnapshot(docSnapshot => {
      for (const change of docSnapshot.docChanges()) {
        const sortedData = lodash.sortBy(change.doc.data().rules, ['index', 'asc']);
        this.rules[change.doc.id] = sortedData.map(item => item.rule);
        this.logger.log(`rules updated to country ${change.doc.id}, rules: ${JSON.stringify(this.rules)}`);
      }
    });
  }

  generateConfig(agent: string, publishedNodes: any[], service: Service, lang: string, client: string, binary = false, isSelfDevClient = false, region = '', protocol = '', country = 'cn') {
    const translationData = this.localeService.translations[lang];

    if (agent === 'none') {
      const nodes =  publishedNodes
          .filter(node => node.type === 'https')
          .map( (n, i) => {
            return {
              name: n.name,
              type: n.type,
              country: n.country,
              status: n.status,
              probe: n.probe || 'http://releases.ubuntu.com',
              description: n.description,
              autopointer: n.autopointer || true,
              published: n.published,
              port: n.port,
              id: n.id || i,
              tags: n.tags,
              host: n.host,
              sni: n.host,
              namespace: n.namespace,
              username: service.username,
              password: service.password,
            };
          });

      const template = Handlebars.compile(JSON.stringify(nodes));
      return JSON.parse(template(translationData));
    }

    if (agent === 'jetstream') {
      let result;
      const nodes = [];
      lodash.each(publishedNodes, (node) => {
        const displayName =  this.addExpireOrRanoutForNode(service, node);
        if (node.type === 'https') {
          nodes.push({
            'name': displayName + `(${node.type})`,
            'username': service.username,
            'password': service.password,
            'type': 'http',
            'server': node.host,
            'port': node.port,
            'tls': true,
            'sni': node.host,
            'skip-cert-verify': true,
          });
        } else if (node.type === 'v2ray') {
          nodes.push({
            'name': displayName + `(${node.type})`,
            'uuid': service.uuid,
            'type': 'vmess',
            'server': node.host,
            'port': node.port,
            'alterId': 0,
            'cipher': 'auto',
            'tls': true,
            'servername': node.sni,
            'skip-cert-verify': true,
            'network': 'h2',
            'h2-opts': {
              host: [node.sni],
              path: '/lzu4t6t5j7qp72ebep0c',
            },
            // 'skip-cert-verify': true,
            // 'network': 'grpc',
            // 'servername': node.host,
            // 'grpc-opts': {
            //   'grpc-service-name': 'lzu4t6t5j7qp72ebep0c',
            // },
            // Give up compatible, have to force user update app
            // // Compatible with the old version of clash, it will be deleted after several updates
            // 'ws-path': `lzu4t6t5j7qp72ebep0c`,
            // 'ws-headers': {
            //   'host': node.host,
            //   'User-Agent': `Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36`,
            // },
            // 'ws-opts': {
            //   path: 'lzu4t6t5j7qp72ebep0c',
            //   headers: {
            //     'host': node.host,
            //     'User-Agent': `Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36`,
            //   },
            // },
          });
        } else if (node.type === 'trojan') {
          nodes.push({
            'name': displayName + `(${node.type})`,
            'server': node.host,
            'port': node.port,
            'type': 'trojan',
            'password': service.uuid,
            'sni': node.sni,
            'alpn': ['h2'],
            'skip-cert-verify': true,
            // 'network': 'grpc',
            // 'servername': node.host,
            // 'grpc-opts': {
            //   'grpc-service-name': 'wikn89urlk8cwtovu5vw',
            // },
            // 'ws-opts': {
            //   'path': '/wikn89urlk8cwtovu5vw',
            //   'headers': {
            //     'Host': node.host,
            //     'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36'
            //   },
            // },
          });
        }
        // else if (node.type === 'ss') {
        //   nodes.push({
        //     'name': displayName + `(${node.type})`,
        //     'server': node.host,
        //     'port': node.port,
        //     'cipher': 'chacha20-ietf-poly1305',
        //     'password': service.uuid,
        //     'udp': true,
        //   });
        // }
      });

      result = lodash.clone(this.jetstreamTemplate);
      result.proxies = nodes;
      result['proxy-groups'][0].proxies = nodes.map(n => n.name);
      const template = Handlebars.compile(YAML.stringify(result));
      return template(translationData);
    }

    if (agent === 'clash') {
      let result;
      const nodes = [];
      // if (!isSelfDevClient) {
      //   nodes = this.addUsageInfoToNode(service, client, nodes);
      // }

      const protocols = protocol ? protocol.split(',') : ['https', 'v2ray', 'trojan', 'ss'];

      const originNodes = lodash.cloneDeep(publishedNodes.filter(n => protocols.includes(n.type)));

      // attach type to name
      if (protocols.length > 1) {
        originNodes.forEach(node => node.name = node.name + `(${node.type})`);
      }

      lodash.each(originNodes.filter(n => protocols.includes(n.type)), (node) => {
        const n = this.handleProtocol(service, node);
        nodes.push(n);
      });

      result = lodash.clone(this.clashTemplate);
      result.proxies = nodes;
      result['proxy-groups'][0].proxies = nodes.map(n => n.name);
      result['subscription-extension'] = [{description: this.generateUsageInfo(service, client)}];

      const template = Handlebars.compile(YAML.stringify(result));
      return template(translationData);
    }

    // override 'clash-grouping'
    if (agent === 'clash-grouping') { agent = 'clash-fallback'; }

    if (['clash-hysteria', 'clash-fallback', 'clash-verge'].includes(agent)) {

      const nodes = [];
      //  deep clone the template

      const result = lodash.cloneDeep(this.fallbackTemplate);

      result['subscription-extension'] = [{description: this.generateUsageInfo(service, client)}];

      // filter node by protocol
      const defaultProtocols = ['https', 'trojan', 'ss'];
      if (agent === 'clash-hysteria' || agent === 'clash-verge') {
        defaultProtocols.push('hysteria');
      }
      const protocols = protocol ? protocol.split(',') : defaultProtocols;
      const originNodes = lodash.cloneDeep(publishedNodes.filter(n => protocols.includes(n.type)));

      // attach type to name
      if (protocols.length > 1) {
        originNodes.forEach(node => node.name = node.name + `(${node.type})`);
      }

      originNodes.forEach(node => node.name = this.addExpireOrRanoutForNode(service, node));

      // filter nodes by the star tag then group by country
      const groupByCountries = lodash(originNodes).filter(n => n.tags.includes('star')).groupBy('country').value();

      const proxyGroups: ProxyGroup[] = lodash.keys(groupByCountries).map(cou => {

        //  this is important shuffle the node, ot
        const sortedNodes = groupByCountries[cou].sort(() => Math.random() - 0.5);
        return {
          name: lang === 'en-US' ? `${this.nodesService.nationalFlag[cou]} ${cou} Auto` : `${this.nodesService.nationalFlag[cou]} ${this.nodesService.countryChineseNames[cou]}自动`,
          type: 'fallback',
          url: 'https://cp.cloudflare.com/generate_204',
          interval: 300,
          proxies: sortedNodes.map(n => n.name),
        };
      });

      const ruleGroupName = lang === 'en-US' ? '🌏 Selected' : '🌏 当前选择';
      const currentOption: ProxyGroup = {
        name: ruleGroupName,
        type: 'select',
        proxies: [...proxyGroups.map(group => group.name), ...originNodes.map(n => n.name) ],
      };

      result['proxy-groups'] = [currentOption, ...proxyGroups];

      // inject service info
      lodash.each(originNodes, (node) => {
        const n = this.handleProtocol(service, node);
        nodes.push(n);
      });

      result.proxies = nodes;

      //  add rules from firestore
      if (this.rules[country]) {
        for ( const  rule of this.rules[country]) {
          result.rules.unshift(rule);
        }
      }
      // put geoip rule to the top
      result.rules.unshift(`GEOIP,${country.toUpperCase()},DIRECT`);

      result.rules.push(`MATCH,${ruleGroupName}`);

      const template = Handlebars.compile(YAML.stringify(result));
      return template(translationData);

    }

    if (agent === 'shadowrocket') {
      const lines = [];
      lines.push(`STATUS=${this.generateUsageInfo(service, client)}`);
      lodash.each(publishedNodes, (node) => {
        const displayName =  this.addExpireOrRanoutForNode(service, node);
        if (node.type === 'https') {
          lines.push('https://' + base64url.default.encode(`${service.username}:${service.password}@${node.host}:${node.port}`) + `?peer=${node.sni}&allowInsecure=1#${displayName}`);
        }  else if (node.type === 'hysteria') {
          lines.push('hysteria2://' + encodeURIComponent(`${service.username}:${service.password}`) + `@${node.host}:${node.port}` + `?insecure=1&obfs=salamander&obfs-password=ebc80948-b76f-450f-9861-d7fae2043e13#${displayName}`);
        } else if (node.type === 'v2ray') {
          lines.push('vmess://' + base64url.default.encode(`auto:${service.uuid}@${node.host}:${node.port}`) + `?remarks=${displayName}&obfsParam=${node.sni}&path=/lzu4t6t5j7qp72ebep0c&obfs=h2&tls=1&peer=${node.sni}&allowInsecure=1&alterId=0`);
        } else if (node.type === 'trojan') {
          lines.push(`trojan://${service.uuid}@${node.host}:${node.port}?allowInsecure=1&peer=${node.sni}&alpn=h2#${displayName}`);
        } else if (node.type === 'ss') {
          lines.push('ss://' + base64url.default.encode(`chacha20-ietf-poly1305:${service.uuid}@${node.host}:${node.port}`) + `#${displayName}(${node.type})`);
        }
      });
      const template = Handlebars.compile(lodash.join(lines, os.EOL));
      const translated = template(translationData);
      return base64url.default.encode(translated);
    }

    if (agent === 'quantumult') {
      const lines = [];
      lodash.each(publishedNodes, (node) => {
        const displayName =  this.addExpireOrRanoutForNode(service, node);
        if (node.type === 'https') {
          lines.push('http://' + base64url.default.encode(`${displayName}(${node.type}) = http, upstream-proxy-address=${node.host}, upstream-proxy-port=${node.port},group=${service.client}-${service.id},upstream-proxy-auth=true, upstream-proxy-username=${service.username}, upstream-proxy-password=${service.password}, over-tls=true, tls-host=${node.sni}, certificate=true`));
        } else if (node.type === 'ss') {
          lines.push('ss://' + base64url.default.encode(`chacha20-ietf-poly1305:${service.uuid}`) + `@${node.host}:${node.port}` + `#${displayName}(${node.type})`);
          // quantumult doesn't support grpc yet, disable now
          // } else if (node.type === 'v2ray') {
          //   lines.push('vmess://' + base64url.default.encode(`${displayName}(${node.type}) = vmess,${node.host},${node.port},auto,"${service.uuid}",group=${service.client}-${service.id},over-tls=true,tls-host=${node.host},host=${node.host},certificate=1,obfs=grpc,obfs-path="/lzu4t6t5j7qp72ebep0c",obfs-header="Host:${node.host}[Rr][Nn]User-Agent:Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36"`));
        }
      });
      const template = Handlebars.compile(lodash.join(lines, os.EOL));
      const translated = template(translationData);
      return  base64url.default.encode(translated);
    }

    if (agent === 'quantumultx') {
      const lines = [];
      lodash.each(publishedNodes, (node) => {
        const displayName =  this.addExpireOrRanoutForNode(service, node);
        if (node.type === 'https') {
          lines.push(`http=${node.host}:${node.port}, username=${service.username}, password=${service.password}, over-tls=true, tls-host=${node.sni}, tls-verification=true, tls13=true, fast-open=false, udp-relay=false, tag=${displayName}`);
        }  else if (node.type === 'ss') {
          lines.push('ss://' + base64url.default.encode(`chacha20-ietf-poly1305:${service.uuid}`) + `@${node.host}:${node.port}` + `#${displayName}(${node.type})`);
          //  quantumultx doesn't support grpc yet, disable now
          // } else if (node.type === 'v2ray') {
          //   lines.push(`vmess=${node.host}:${node.port}, method=aes-128-gcm, password=${service.uuid}, obfs-host=${node.host}, obfs=over-tls, tls-verification=true, path=/lzu4t6t5j7qp72ebep0c,fast-open=false, udp-relay=false, tag=${displayName}(${node.type})`);
        } else if (node.type === 'trojan') {
          lines.push(`trojan=${node.host}:${node.port}, password=${service.uuid},  over-tls=true, tls-host=${node.host}, tag=${displayName}(${node.type})`);
        }
      });
      const template = Handlebars.compile(lodash.join(lines, os.EOL));
      return template(translationData);
    }

    if (agent === 'surge') {
      const lines = [];
      lines.push(this.surgeHeaderTemplate);
      lines.push('[Proxy]');
      const groupNames = ['PROXY = select'];
      lodash.each(publishedNodes, (node) => {
        const displayName =  this.addExpireOrRanoutForNode(service, node);
        if (node.type === 'https') {
          lines.push(`${displayName}(${node.type}) = ${node.type},${node.host},${node.port},username=${service.username},password=${service.password},tls=true,skip-cert-verify=true,sni=${node.sni}`);
          groupNames.push(`${displayName}(${node.type})`);
        } else if (node.type === 'ss') {
          lines.push(`${displayName}(${node.type}) = ss,${node.host},${node.port},encrypt-method=chacha20-ietf-poly1305,password=${service.uuid},tfo=false,udp-relay=true`);
          groupNames.push(`${displayName}(${node.type})`);
          // Surge 暂不支持 grpc
          // } else if (node.type === 'v2ray') {
          //   lines.push(`${displayName}(${node.type}) = vmess,${node.host},${node.port},username=${service.uuid},tls=true,ws=true,ws-path=/lzu4t6t5j7qp72ebep0c,ws-headers=Host:${node.host}|User-Agent:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36`);
          //   groupNames.push(`${displayName}(${node.type})`);
        } else if (node.type === 'trojan') {
          lines.push(`${displayName}(${node.type}) = trojan,${node.host},${node.port},password=${service.uuid},sni=${node.sni},skip-cert-verify=true`);
          groupNames.push(`${displayName}(${node.type})`);
        }
      });

      lines.push( '[Proxy Group]');
      lines.push(groupNames.join(','));

      lines.push(this.surgeFooterTemplate);
      const template = Handlebars.compile(lodash.join(lines, os.EOL));
      return template(translationData);
    }

    if (agent === 'switchyomega') {
      const result = {
        '-addConditionsToBottom': false,
        '-confirmDeletion': true,
        '-downloadInterval': 1440,
        '-enableQuickSwitch': false,
        '-monitorWebRequests': true,
        '-quickSwitchProfiles': [],
        '-refreshOnProfileChange': true,
        '-revertProxyChanges': true,
        '-showExternalProfile': true,
        '-showInspectMenu': true,
        '-startupProfileName': '',
        'schemaVersion': 2,
      };
      lodash.forEach(publishedNodes, (node) => {
        if (node.type === 'https') {
          const displayName =  this.addExpireOrRanoutForNode(service, node);
          result[`+${displayName}`] = { auth: { fallbackProxy: { password: service.password, username: service.username } }, bypassList: [{ conditionType: 'BypassCondition', pattern: '127.0.0.1' }, { conditionType: 'BypassCondition', pattern: '[::1]' }, { conditionType: 'BypassCondition', pattern: 'localhost' }], color: '#ffaa88', fallbackProxy: { host: node.host, port: node.port, scheme: 'https' }, name: displayName, profileType: 'FixedProfile', revision: '16be549cc0f' };
        }
      });
      const template = Handlebars.compile(JSON.stringify(result));
      return template(translationData);
    }

    if (agent === 'v2rayu') {
      const lines = [];
      lodash.each(publishedNodes,  (node) => {
        const displayName =  this.addExpireOrRanoutForNode(service, node);
        if (node.type === 'v2ray') {
          lines.push('vmess://' + base64url.default.encode(`{"v":"2","ps":"${displayName}(${node.type})","add":"${node.host}","port":"${node.port}","id":"${service.uuid}","aid":"0","net":"h2","type":"none","host":"${node.host}","path":"/lzu4t6t5j7qp72ebep0c","tls":"none"}`));
        }
      });
      return base64url.default.encode(lodash.join(lines, os.EOL));
    }

    if (agent === 'leaf') {
      const nodes = [];
      const nodeNames = [];
      lodash.each(publishedNodes, (node) => {
        const displayName =  this.addExpireOrRanoutForNode(service, node);
        switch (node.type) {
            // case 'v2ray':
            //   nodeNames.push(`${displayName}(${node.type})`);
            //   nodes.push(`${displayName}(${node.type}) = vmess, ${node.host}, ${node.port}, username=${service.uuid}, ws=true, tls=true, ws-path=i60fgnr6zvbtt2kjhd7x`);
            //   break;
          case 'trojan':
            nodeNames.push(`${displayName}(${node.type})`);
            nodes.push(`${displayName}(${node.type}) = trojan, ${node.host}, ${node.port}, password=${service.uuid}, sni=${node.host}`);
            break;
          case 'ss':
            nodeNames.push(`${displayName}(${node.type})`);
            nodes.push(`${displayName}(${node.type}) = ss, ${node.host}, ${node.port}, encrypt-method=chacha20-ietf-poly1305, password=${service.uuid}`);
            break;
        }
      });

      let result = lodash.clone(this.leafTemplate);
      const template = Handlebars.compile(result);
      if (binary) {
        result = template({
          proxy: nodes.join('\n'),
          proxyNames: nodeNames.join(', '),
          binaryConfig: '\n# Local HTTP CONNECT proxy\n' +
              'interface = 127.0.0.1\n' +
              'port = 1087\n' +
              '\n' +
              '# Local SOCKS5 proxy with UDP Associate support\n' +
              'socks-interface = 127.0.0.1\n' +
              'socks-port = 1086',
        });
      } else {
        result = template({proxy: nodes.join('\n'), proxyNames: nodeNames.join(', '), binaryConfig: ''});
      }
      return result;
    }

    if (agent === 'surfboard') {
      const nodes = [];
      const nodeNames = [];
      lodash.each(publishedNodes, (node) => {
        const displayName =  this.addExpireOrRanoutForNode(service, node);
        switch (node.type) {
          case 'https':
            nodeNames.push(`${displayName}(${node.type})`);
            nodes.push(`${displayName}(${node.type}) = https, ${node.host}, ${node.port}, ${service.username}, ${service.password}, skip-cert-verify=true, sni=${node.sni}, tfo=false, udp-relay=false`);
            break;
          case 'ss':
            nodeNames.push(`${displayName}(${node.type})`);
            nodes.push(`${displayName}(${node.type}) = ss, ${node.host}, ${node.port}, encrypt-method=chacha20-ietf-poly1305, password=${service.uuid}, tfo=false, udp-relay=true`);
            break;
          case 'trojan':
            nodeNames.push(`${displayName}(${node.type})`);
            nodes.push(`${displayName}(${node.type}) = trojan, ${node.host}, ${node.port}, password=${service.uuid}, sni=${node.sni}, skip-cert-verify=true`);
            break;
        }
      });

      let result = lodash.clone(this.surfboardTemplate);
      const template = Handlebars.compile(result);
      const subUrl = `${this.getSubscriptionUrlWithoutAgent(client, service.clientId)}?agent=surfboard`;
      result = template({proxy: nodes.join('\n'), proxyNames: nodeNames.join(', '), subscriptionUrl: subUrl});
      return result;
    }

    if (agent === 'grouping') {
      let result;
      let nodes = [];
      if (!(region && protocol)) {
        throw new HttpException({
          error: 'InvalidParameter',
          message: 'Region and protocol are required',
        }, HttpStatus.BAD_REQUEST);
      }
      if (region.includes('NODE')) {
        nodes = this.addUsageInfoToNode(service, client, []);
      }
      const filterNodes =  publishedNodes.filter((node) => {
        const regions = region.split(',');
        const protocols = protocol.split(',');
        return regions.includes(node.country) && protocols.includes(node.type);
      });

      const originNodes = lodash.cloneDeep(filterNodes);

      // attach type to name
      originNodes.forEach(node => node.name = node.name + `(${node.type})`);

      lodash.each(originNodes, (node) => {
        // attach type to name
        node.name = node.name + `(${node.type})`;
        const n = this.handleProtocol(service, node);
        nodes.push(n);
      });
      result = lodash.clone(this.groupingTemplate);
      result.proxies = nodes;
      const template = Handlebars.compile(YAML.stringify(result));
      return template(translationData);

    }
    throw new HttpException({
      error: 'NotSupporting',
      message: 'We do not support this client',
    }, HttpStatus.BAD_REQUEST);
  }

  getNodeTags(node): string[] | undefined {
    if (node.tags && node.tags.length > 0) {
      // Do not simplify this code, it will lead to json to yaml conversion error!!!
      return [...node.tags];
    }
    return undefined;
  }

  // for clash only
  handleProtocol(service: Service, node) {
    const tags = this.getNodeTags(node);
    const loadCredential = {
      'load-username': service.username,
      'load-password': service.password,
    };
    if (node.type === 'https') {
      // tags存在时才返回tags，否则不添加tags
      const info = {
        'name': node.name,
        'username': service.username,
        'password': service.password,
        'type': 'http',
        'server': node.host,
        'port': node.port,
        'tls': true,
        'sni': node.sni,
        'skip-cert-verify': true,
      };
      return tags ? {...info, tags, ...loadCredential} : {...info, ...loadCredential};
    } else if (node.type === 'v2ray') {
      const info = {
        'name': node.name,
        'uuid': service.uuid,
        'type': 'vmess',
        'server': node.host,
        'port': node.port,
        'alterId': 0,
        'cipher': 'auto',
        'tls': true,
        'servername': node.sni,
        'skip-cert-verify': true,
        'network': 'h2',
        'h2-opts': {
          host: [node.sni],
          path: '/lzu4t6t5j7qp72ebep0c',
        },
      };
      return tags ? {...info, tags, ...loadCredential} : {...info, ...loadCredential};
    } else if (node.type === 'trojan') {
      const info = {
        'name': node.name,
        'server': node.host,
        'port': node.port,
        'type': 'trojan',
        'password': service.uuid,
        'sni': node.sni,
        'alpn': ['h2'],
        'skip-cert-verify': true,
        // 'ws-opts': {
        //   'path': '/wikn89urlk8cwtovu5vw',
        //   'headers': {
        //     'Host': node.host,
        //     'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36'
        //   },
        // },
      };
      return tags ? {...info, tags, ...loadCredential} : {...info, ...loadCredential};
    } else if (node.type === 'hysteria') {
      const info = {
        'name': node.name,
        'type': 'hysteria2',
        'server': node.host,
        'port': node.port,
        'password': `${service.username}:${service.password}`,
        'obfs': 'salamander',
        'obfs-password': 'ebc80948-b76f-450f-9861-d7fae2043e13',
        'skip-cert-verify': true,
      };
      return tags ? {...info, tags, ...loadCredential} : {...info, ...loadCredential};
    } else if (node.type === 'ss') {
      const info = {
        name: node.name,
        server: node.host,
        port: node.port,
        type: 'ss',
        cipher: 'chacha20-ietf-poly1305',
        password: service.uuid,
      };
      return tags ? {...info, tags, ...loadCredential} : {...info, ...loadCredential};
    }
  }

  private generateUsageInfo(service: Service, client: string): string {
    const sum = (Number(service.trafficDownload) + Number(service.trafficUpload));
    const count = (sum / 1024 / 1024 / 1024).toFixed(2);
    const total = JSON.parse(service.trafficQuota)[0].traffic / 1024 / 1024 / 1024;
    const expiredAt = moment.utc(service.expiredAt).format('YYYY-MM-DD');
    // let homepage = 'jetstream.tel';
    // if (/jetstream/.test(client)) {
    //   homepage = 'jetstream.tel';
    // }
    // if (/flash/.test(client)) {
    //   homepage = 'flashvpn.io';
    // }
    // if (/ztnet/.test(client)) {
    //   homepage = 'ztnet.xyz';
    // }
    return `用量： ${count} GB / ${total} GB, 过期时间：${expiredAt}`;
  }

  private addUsageInfoToNode(service: Service, client: string, nodes = []) {
    const count = Number(service.trafficDownload) + Number(service.trafficUpload);
    const total = JSON.parse(service.trafficQuota)[0].traffic;
    const expiredAt = moment.utc(service.expiredAt).format('YYYY-MM-DD');
    const percentage = Math.round(count / total * 100);
    if (/jetstream/.test(client)) {
      nodes.push({ 'name': '官网：https://jetstream.tel, https://jetstream.surf', 'username': 'username', 'password': 'password', 'type': 'http', 'server': 'localhost', 'port': 80, 'tls': true, 'skip-cert-verify': true });
    }

    if (/flash/.test(client)) {
      nodes.push({ 'name': '续费网址：https://jss888.co', 'username': 'username', 'password': 'password', 'type': 'http', 'server': 'localhost', 'port': 80, 'tls': true, 'skip-cert-verify': true });
      nodes.push({ 'name': '备用网址：https://tuiba.xyz', 'username': 'username', 'password': 'password', 'type': 'http', 'server': 'localhost', 'port': 80, 'tls': true, 'skip-cert-verify': true });
    }

    if (/ztnet/.test(client)) {
      nodes.push({ 'name': '官网：https://ztnet.xyz', 'username': 'username', 'password': 'password', 'type': 'http', 'server': 'localhost', 'port': 80, 'tls': true, 'skip-cert-verify': true });
    }
    nodes.push({ 'name': `服务：已用${percentage}%，${expiredAt}过期`, 'username': 'username', 'password': 'password', 'type': 'http', 'server': 'localhost', 'port': 80, 'tls': true, 'skip-cert-verify': true });
    return nodes;
  }

  private getBrandName(client: string): string {
    if (/jetstream/.test(client)) {
      return 'JetStream';
    }
    if (/flash/.test(client)) {
      return 'FlashVPN';
    }
    if (/ztnet/.test(client)) {
      return 'ZTNet';
    }
    if (/rubiz/.test(client)) {
      return 'Rubiz';
    }
    return 'JetStream';
  }

  private getSubscriptionUrlWithoutAgent(client: string, serviceId: number): string {

    if (process.env.NODE_ENV !== 'prod') {
      const apiHost = 'https://uat-api.droplet.today';
      return `${apiHost}/api/v1/services/${client}/${serviceId}/subscriptionV2`;
    } else {
      let apiHost = 'https://jetflow.site';
      if (client.match('flash')) {
        apiHost = 'https://lightning.cool';
      }
      if (client.match('ztnet')) {
        apiHost = 'https://lcx.one';
      }
      if (client.match('rubiz')) {
        apiHost = 'https://rubiz.site';
      }
      const subUrl = `${apiHost}/api/v1/public/services/${serviceId}/${crypto.createHash('md5').update(`${serviceId}-jss-is-fucking-awesome`).digest('hex')}`;
      return subUrl;
    }

  }

  private addExpireOrRanoutForNode(service: Service, node: any): string {

    // If the node name contains Chinese, return directly
    if (node.name.match(/[\u4e00-\u9fa5]/)) {
      return node.name;
    }

    if (service.isDue()) {
      return  node.name + '[expired]' ;
    }

    if (service.isUsedUp()) {
      return  node.name + '[ran-out]' ;
    }

    return  node.name;
  }
}

interface ProxyGroup {
  name: string;
  type: 'select' | 'fallback' | 'url-test' | 'load-balance';
  proxies?: string[];
  url?: string;
  interval?: number;
}
