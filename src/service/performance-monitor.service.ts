import { Injectable, Logger } from '@nestjs/common';
import * as os from 'os';

export interface PerformanceMetrics {
  timestamp: Date;
  memoryUsage: NodeJS.MemoryUsage;
  cpuUsage: number;
  activeConnections: number;
  requestsPerSecond: number;
  averageResponseTime: number;
  dailyUsageOperations: {
    cacheHits: number;
    cacheMisses: number;
    dbWrites: number;
    errors: number;
  };
}

@Injectable()
export class PerformanceMonitorService {
  private readonly logger = new Logger(PerformanceMonitorService.name);
  private metrics: PerformanceMetrics[] = [];
  private requestCount = 0;
  private totalResponseTime = 0;
  private lastRequestTime = Date.now();
  private dailyUsageStats = {
    cacheHits: 0,
    cacheMisses: 0,
    dbWrites: 0,
    errors: 0,
  };

  constructor() {
    // 每分钟收集一次性能指标
    setInterval(() => {
      this.collectMetrics();
    }, 60000);

    // 每10分钟输出性能报告
    setInterval(() => {
      this.logPerformanceReport();
    }, 600000);
  }

  /**
   * 记录请求开始时间
   */
  startRequest(): number {
    return Date.now();
  }

  /**
   * 记录请求结束时间并更新统计
   */
  endRequest(startTime: number): void {
    const responseTime = Date.now() - startTime;
    this.requestCount++;
    this.totalResponseTime += responseTime;
  }

  /**
   * 记录每日用量缓存命中
   */
  recordCacheHit(): void {
    this.dailyUsageStats.cacheHits++;
  }

  /**
   * 记录每日用量缓存未命中
   */
  recordCacheMiss(): void {
    this.dailyUsageStats.cacheMisses++;
  }

  /**
   * 记录数据库写入操作
   */
  recordDbWrite(): void {
    this.dailyUsageStats.dbWrites++;
  }

  /**
   * 记录错误
   */
  recordError(): void {
    this.dailyUsageStats.errors++;
  }

  /**
   * 收集性能指标
   */
  private collectMetrics(): void {
    const now = Date.now();
    const timeDiff = (now - this.lastRequestTime) / 1000; // 秒
    const requestsPerSecond = timeDiff > 0 ? this.requestCount / timeDiff : 0;
    const averageResponseTime = this.requestCount > 0 ? this.totalResponseTime / this.requestCount : 0;

    const metrics: PerformanceMetrics = {
      timestamp: new Date(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: this.getCpuUsage(),
      activeConnections: this.getActiveConnections(),
      requestsPerSecond,
      averageResponseTime,
      dailyUsageOperations: { ...this.dailyUsageStats },
    };

    this.metrics.push(metrics);

    // 只保留最近100个指标点
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100);
    }

    // 重置计数器
    this.requestCount = 0;
    this.totalResponseTime = 0;
    this.lastRequestTime = now;
    this.dailyUsageStats = {
      cacheHits: 0,
      cacheMisses: 0,
      dbWrites: 0,
      errors: 0,
    };
  }

  /**
   * 获取CPU使用率
   */
  private getCpuUsage(): number {
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;

    cpus.forEach(cpu => {
      for (const type in cpu.times) {
        totalTick += cpu.times[type];
      }
      totalIdle += cpu.times.idle;
    });

    return 100 - (100 * totalIdle / totalTick);
  }

  /**
   * 获取活跃连接数（简化版本）
   */
  private getActiveConnections(): number {
    // 这里可以根据实际情况实现
    // 例如从数据库连接池或HTTP服务器获取
    return 0;
  }

  /**
   * 输出性能报告
   */
  private logPerformanceReport(): void {
    if (this.metrics.length === 0) {
      return;
    }

    const latest = this.metrics[this.metrics.length - 1];
    const memoryMB = {
      rss: Math.round(latest.memoryUsage.rss / 1024 / 1024),
      heapUsed: Math.round(latest.memoryUsage.heapUsed / 1024 / 1024),
      heapTotal: Math.round(latest.memoryUsage.heapTotal / 1024 / 1024),
      external: Math.round(latest.memoryUsage.external / 1024 / 1024),
    };

    this.logger.log(`Performance Report:
      Memory Usage: RSS=${memoryMB.rss}MB, Heap=${memoryMB.heapUsed}/${memoryMB.heapTotal}MB, External=${memoryMB.external}MB
      CPU Usage: ${latest.cpuUsage.toFixed(2)}%
      Requests/sec: ${latest.requestsPerSecond.toFixed(2)}
      Avg Response Time: ${latest.averageResponseTime.toFixed(2)}ms
      Daily Usage Ops: Cache Hits=${latest.dailyUsageOperations.cacheHits}, Misses=${latest.dailyUsageOperations.cacheMisses}, DB Writes=${latest.dailyUsageOperations.dbWrites}, Errors=${latest.dailyUsageOperations.errors}
    `);
  }

  /**
   * 获取当前性能指标
   */
  getCurrentMetrics(): PerformanceMetrics | null {
    return this.metrics.length > 0 ? this.metrics[this.metrics.length - 1] : null;
  }

  /**
   * 获取历史性能指标
   */
  getHistoricalMetrics(limit: number = 10): PerformanceMetrics[] {
    return this.metrics.slice(-limit);
  }

  /**
   * 检查是否存在性能问题
   */
  checkPerformanceIssues(): string[] {
    const issues: string[] = [];
    const latest = this.getCurrentMetrics();

    if (!latest) {
      return issues;
    }

    // 内存使用检查
    const heapUsedMB = latest.memoryUsage.heapUsed / 1024 / 1024;
    if (heapUsedMB > 500) {
      issues.push(`High memory usage: ${heapUsedMB.toFixed(2)}MB`);
    }

    // CPU使用检查
    if (latest.cpuUsage > 80) {
      issues.push(`High CPU usage: ${latest.cpuUsage.toFixed(2)}%`);
    }

    // 响应时间检查
    if (latest.averageResponseTime > 1000) {
      issues.push(`Slow response time: ${latest.averageResponseTime.toFixed(2)}ms`);
    }

    // 错误率检查
    const totalOps = latest.dailyUsageOperations.cacheHits + 
                    latest.dailyUsageOperations.cacheMisses + 
                    latest.dailyUsageOperations.dbWrites;
    if (totalOps > 0) {
      const errorRate = latest.dailyUsageOperations.errors / totalOps;
      if (errorRate > 0.05) { // 5%错误率
        issues.push(`High error rate: ${(errorRate * 100).toFixed(2)}%`);
      }
    }

    return issues;
  }
}
