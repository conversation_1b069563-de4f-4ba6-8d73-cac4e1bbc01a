import { Body, Controller, Get, HttpException, HttpStatus, <PERSON>gger, Param, Post, Query, Req, Res } from '@nestjs/common';

import { IsIn, IsNotEmpty, IsOptional } from 'class-validator';
import { Service, ServiceResetType, ServiceStatus } from './service.entity';
import { ConfigService } from '../config/config.service';
import * as moment from 'moment';
import { ServiceService } from './service.service';
import { ProxyService } from './proxy.service';
import { DailyUsageService } from './daily-usage.service';
import { PerformanceMonitorService } from './performance-monitor.service';
import {NodesInfos, NodesService} from '../nodes/nodes.service';
import * as Big from 'big.js';

class SubscriptionV2DTO {

  @IsNotEmpty()
  @IsIn(['none', 'clash', 'surge', 'shadowrocket', 'quantumult', 'v2rayu', 'switchyomega', 'quantumultx', 'jetstream', 'leaf', 'surfboard', 'grouping', 'clash-grouping', 'clash-fallback', 'clash-hysteria', 'clash-verge'])
  agent: string;

  @IsOptional()
  @IsIn(['yes'])
  allShards: string;

  @IsOptional()
  @IsIn(['yes'])
  binary: string;

  @IsOptional()
  @IsIn(['yes', 'no'])
  isSelfDevClient: string;

  @IsOptional()
  @IsIn(['jetstream', 'flashvpn', 'rubiz', 'ztnet'])
  project: string;

  @IsOptional()
  @IsIn(['yes', 'no'])
  direct: string;

  @IsOptional()
      // @IsIn(['https', 'v2ray', 'trojan', 'ss'])
  protocol: string;

  @IsOptional()
      // @IsIn(['HK', 'JP', 'TW', 'US', 'DE', 'NODE'])
  region: string;

  @IsOptional()
  country: string;

  @IsOptional()
  lang: string = 'zh-CN';
}

class CreateDTO {

  @IsNotEmpty()
  clientId: number;

  @IsNotEmpty()
  client: string;

  @IsNotEmpty()
  quota: string;

  @IsOptional()
  status: ServiceStatus;

  @IsOptional()
  expiredAt: string;

  @IsOptional()
  @IsIn([0, 1])
  resetType: number;
}

class UsageDTO {
  @IsNotEmpty()
  delta: number;
}

class UpdateDTO {

  @IsNotEmpty()
  clientId: string;

  @IsNotEmpty()
  client: string;

  @IsOptional()
  expiredAt: string;

  @IsOptional()
  status: ServiceStatus;

  @IsOptional()
  quota: string;
}

class ResetDTO {
  @IsNotEmpty()
  clientId: number;

  @IsNotEmpty()
  client: string;
}

class DailyUsageQueryDTO {
  @IsNotEmpty()
  clientId: number;

  @IsNotEmpty()
  client: string;

  @IsOptional()
  startDate: string; // YYYY-MM-DD

  @IsOptional()
  endDate: string; // YYYY-MM-DD
}

@Controller('api/v1/services')
export class ServicesController {

  private readonly logger = new Logger(ServicesController.name);
  constructor(
      private config: ConfigService,
      private serviceService: ServiceService,
      private proxyService: ProxyService,
      private nodesService: NodesService,
      private dailyUsageService: DailyUsageService,
      private performanceMonitor: PerformanceMonitorService,
  ) {
  }

  /**
   * @api {post} /api/v1/services Create a new service
   * @apiName CreateService
   * @apiGroup Service
   *
   * @apiBody {Number} clientId user's serviceId in whmcs.
   * @apiBody {String="jetstream", "jetstream-uat", "flash", "flash-uat", "rubiz", "rubiz-uat", "lightning", "lightning-uat"} client product name.
   * @apiBody {String} quota service's quota. example: [{\"traffic\":************,\"speed\":3932160}].
   * @apiBody {Number="0", "1"} status service's status, 0 means Suspended, 1 means Active.
   * @apiBody {String} expiredAt service's expiredAt. Format: MMMM-YY-DD.
   * @apiBody {Number="0", "1"} resetType service's resetType, 0 means never reset, 1 means reset every month.
   *
   * @apiErrorExample {json} FailedToCreateService
   *   HTTP/1.1 400 Bad Request
   *   {
   *      "error": "FailedToCreateService",
   *      "message": '',
   *   }
   *
   *  @apiSuccessExample {json} Success
   *  HTTP/1.1 201 Created
   *  {
   *   "id": 24536,
   *   "clientId": "1",
   *   "client": "rubiz",
   *   "username": "3bc840880f",
   *   "password": "73e3b6c33d",
   *   "status": 1,
   *   "uuid": "6019ebdf-1749-4386-b06c-36f836c87a96",
   *   "quota": "[{\"traffic\":************,\"speed\":3932160}]",
   *   "type": 1,
   *   "upload": "0",
   *   "download": "0",
   *   "resetDate": "2022-04-08T14:35:42.000Z",
   *   "updatedAt": "1969-12-31T16:00:00.000Z",
   *   "expiredAt": "2022-07-08T00:00:00.000Z",
   *   "speedLimit": -1
   * }
   */
  @Post()
  // @UseGuards(AuthGuard)
  async new(@Body() body: CreateDTO) {

    const service = this.serviceService.newService();

    service.client = body.client;
    service.clientId = body.clientId;

    // reset type
    if (body.resetType === 0) {
      service.trafficResetCycle = ServiceResetType.Never;
      service.expiredAt = moment('2100-01-01').toDate();
    } else {

      service.trafficResetCycle = ServiceResetType.Monthly;

      // expiration
      if (body.expiredAt !== null) {
        const date = moment.utc(body.expiredAt);
        service.expiredAt = date.toDate();
        service.trafficNextResetDate = ServiceService.getNextResetDate(date.format('YYYY-MM-DD'));
      }

    }

    // handle status
    if (body.status) {
      service.status = body.status;
    }

    service.trafficQuota = JSON.stringify(JSON.parse(body.quota));

    try {
      await service.save();
      this.logger.log(`create new service for ${JSON.stringify(body)}`);
      this.serviceService.syncServices(service).then();
      return service.toJson();
    } catch (e) {

      this.logger.error(`Failed to create service due to ${e.message}`);
      if (e.code === 'ER_DUP_ENTRY') {
        throw new HttpException({
          error: 'ServiceExists',
          message: 'Service already exists, please check your input',
        }, HttpStatus.BAD_REQUEST);
      }
      throw new HttpException({
        error: 'FailedToCreateService',
        message: '',
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }

  }

  /**
   * @api {post} /api/v1/services/updatePassword Update service's password
   * @apiName UpdatePassword
   * @apiGroup Service
   *
   * @apiBody {Number} clientId user's serviceId in whmcs.
   * @apiBody {String="jetstream", "jetstream-uat", "flash", "flash-uat", "rubiz", "rubiz-uat", "lightning", "lightning-uat"} client product name.
   *
   * @apiErrorExample {json} BadRequest
   *   HTTP/1.1 400 Bad Request
   *   {
   *     "statusCode": 400,
   *     "error": "Bad Request"
   *   }
   * @apiErrorExample {json} NoSuchService
   *  HTTP/1.1 400 Bad Request
   *  {
   *    "error": "NoSuchService",
   *    "message": "We do not have this service"
   *  }
   *
   *  @apiSuccessExample {text} Success
   *  HTTP/1.1 201 Created
   */
  @Post('updatePassword')
  // @UseGuards(AuthGuard)
  async updatePassword(@Body() body: ResetDTO) {

    const id = body.clientId;
    const client = body.client;

    const services = await Service.find({ where: {
        clientId: id,
        client,
      }});
    if (services.length !== 0) {
      const service = services.pop();
      this.serviceService.resetPassword(service);
      await service.save();
      this.serviceService.syncServices(service);
      this.logger.log(`update password for ${JSON.stringify(body)}`);
    }

    return '';
  }

  /**
   * @api {post} /api/v1/services/update Update service
   * @apiName UpdateService
   * @apiGroup Service
   *
   * @apiBody {Number} clientId user's serviceId in whmcs.
   * @apiBody {String="jetstream", "jetstream-uat", "flash", "flash-uat", "rubiz", "rubiz-uat", "lightning", "lightning-uat"} client product name.
   * @apiBody {String} quota service's quota. example: [{\"traffic\":************,\"speed\":3932160}].
   * @apiBody {Number="0", "1"} status service's status, 0 means Suspended, 1 means Active.
   * @apiBody {String} expiredAt service's expiredAt. Format: MMMM-YY-DD.
   *
   * @apiErrorExample {json} BadRequest
   *  HTTP/1.1 400 Bad Request
   *  {
   *    "statusCode": 400,
   *    "error": "Bad Request"
   *  }
   *  @apiErrorExample {json} NoSuchService
   *  HTTP/1.1 400 Bad Request
   *  {
   *    "error": "NoSuchService",
   *    "message": "We do not have this service"
   *  }
   *
   *  @apiSuccessExample {json} Success
   *  HTTP/1.1 201 Created
   *  {
   *    "id": 24536,
   *    "clientId": "1",
   *    "client": "rubiz",
   *    "username": "3bc840880f",
   *    "password": "73e3b6c33d",
   *    "status": 1,
   *    "uuid": "6019ebdf-1749-4386-b06c-36f836c87a96",
   *    "quota": "[{\"traffic\":************,\"speed\":3932160}]",
   *    "type": 1,
   *    "upload": "0",
   *    "download": "0",
   *    "resetDate": "2022-04-08T14:35:42.000Z",
   *    "updatedAt": "1969-12-31T16:00:00.000Z",
   *    "expiredAt": "2022-07-08T00:00:00.000Z",
   *    "speedLimit": 0
   * }
   */
  @Post('update')
  // @UseGuards(AuthGuard)
  async update(@Body() body: UpdateDTO) {

    this.logger.log(`Start to update service ${JSON.stringify(body)}`);

    const services = await Service.find({ where: {client: body.client, clientId: body.clientId} });
    if (services.length !== 0) {
      const service = services[0];

      // reset type
      if (service.trafficResetCycle === ServiceResetType.Never) {
        service.expiredAt = moment('2100-01-01').toDate();
      } else {

        // handle expiration and reset date
        const newExpireDate = moment.utc(body.expiredAt);
        const today = moment.utc();
        const oldExpireDate = moment.utc(service.expiredAt);
        if (body.expiredAt && (newExpireDate.format('YYYY-MM-DD') !== oldExpireDate.format('YYYY-MM-DD') )) {
          service.trafficNextResetDate = ServiceService.getNextResetDate(body.expiredAt);
          service.expiredAt = newExpireDate.toDate();
          if (oldExpireDate.format('YYYY-MM-DD') <= today.format('YYYY-MM-DD')) {
            service.reset();
            // 异步处理服务重置时的每日用量
            setImmediate(async () => {
              try {
                await this.dailyUsageService.handleServiceReset(service);
              } catch (error) {
                this.logger.error(`Failed to handle service reset for daily usage of service ${service.id}: ${error.message}`);
              }
            });
          }
        }
      }

      // handle status
      if (body.status !== undefined) {
        service.status = body.status;
      }

      if (body.quota) {
        const quota = JSON.parse(service.trafficQuota);
        const download = new Big(service.trafficDownload);
        const upload = new Big(service.trafficUpload);
        if (download.add(upload).gte(quota[0].traffic)) {
          service.trafficDownload = quota[0].traffic;
          service.trafficUpload = 0;
        }
        service.trafficQuota = JSON.stringify(JSON.parse(body.quota));
      }

      await service.save();
      this.logger.log(`update service for ${JSON.stringify(body)}`);
      this.serviceService.syncServices(service);
      return service.toJson();
    }
    throw new HttpException({
      error: 'NoSuchService',
      message: '',
    }, HttpStatus.NOT_FOUND);
  }

  @Get('infos')
  async getNodeInfos(): Promise<NodesInfos> {
    return this.nodesService.nodesInfos;
  }

  /**
   * @api {post} /api/v1/services/:client/:id/updateQuota Update service quota
   * @apiName UpdateServiceQuota
   * @apiGroup Service
   *
   * @apiParam {Number} clientId user's serviceId in whmcs.
   * @apiParam {String="jetstream", "jetstream-uat", "flash", "flash-uat", "rubiz", "rubiz-uat", "lightning", "lightning-uat"} client product name.
   * @apiBody {Number} delta used traffic.
   *
   * @apiErrorExample {} BadRequest
   * HTTP/1.1 201 Created
   *
   * @apiSuccessExample {json} Success
   *  HTTP/1.1 201 Created
   *  {
   *   "id": 24536,
   *   "clientId": 1,
   *   "client": "rubiz",
   *   "username": "1d7341d729",
   *   "password": "134128b1a6",
   *   "status": 0,
   *   "uuid": "6b4025fb-9b85-4626-90db-e34ed7bb688b",
   *   "quota": "[{\"traffic\":************,\"speed\":3932160}]",
   *   "type": 1,
   *   "upload": "0",
   *   "download": "0",
   *   "resetDate": "2022-04-08T14:47:59.000Z",
   *   "updatedAt": "1969-12-31T16:00:00.000Z",
   *   "expiredAt": "2022-07-08T00:00:00.000Z",
   *   "speedLimit": 0
   *  }
   */
  @Post(':client/:id/updateQuota')
  // @UseGuards(AuthGuard)
  async updateQuota(@Body() body: UsageDTO, @Param() params) {

    const services = await Service.find({ where: { clientId: params.id, client: params.client} });
    if (services.length !== 0) {
      const service = services[0];
      service.updateQuota(body.delta);
      await service.save();
      this.logger.log(`update quota for ${JSON.stringify(body)}`);
      this.serviceService.syncServices(service);
      return service.toJson();
    }
    return '';
  }

  /**
   * @api {post} /api/v1/services/:client/:id/reduceUsage Reduce usage
   * @apiName ReduceUsage
   * @apiGroup Service
   *
   * @apiParam {Number} clientId user's serviceId in whmcs.
   * @apiParam {String="jetstream", "jetstream-uat", "flash", "flash-uat", "rubiz", "rubiz-uat", "lightning", "lightning-uat"} client product name.
   * @apiBody {Number} delta traffic that needs to be reduced.
   *
   * @apiErrorExample {} BadRequest
   * HTTP/1.1 201 Created
   *
   *  @apiSuccessExample {json} Success
   *  HTTP/1.1 201 Created
   *  {
   *    "id": 24536,
   *    "clientId": "1",
   *    "client": "rubiz",
   *    "username": "3bc840880f",
   *    "password": "73e3b6c33d",
   *    "status": 1,
   *    "uuid": "6019ebdf-1749-4386-b06c-36f836c87a96",
   *    "quota": "[{\"traffic\":************,\"speed\":3932160}]",
   *    "type": 1,
   *    "upload": "0",
   *    "download": "0",
   *    "resetDate": "2022-04-08T14:35:42.000Z",
   *    "updatedAt": "1969-12-31T16:00:00.000Z",
   *    "expiredAt": "2022-07-08T00:00:00.000Z",
   *    "speedLimit": 0
   * }
   */
  @Post(':client/:id/reduceUsage')
  // @UseGuards(AuthGuard)
  async reduceUsage(@Body() body: UsageDTO, @Param() params) {

    const service = await Service.findOne({ where: { clientId: params.id, client: params.client} });
    if (service) {
      service.reduceUsage(body.delta);
      await service.save();
      this.logger.log(`reduceUsage usage for ${JSON.stringify(body)}`);
      this.serviceService.syncServices(service);
      return service.toJson();
    }
    return '';
  }

  /**
   * @api {get} /api/v1/services/show Get service
   * @apiName GetService
   * @apiGroup Service
   *
   * @apiParam {Number} clientId user's serviceId in whmcs.
   * @apiParam {String="jetstream", "jetstream-uat", "flash", "flash-uat", "rubiz", "rubiz-uat", "lightning", "lightning-uat"} client product name.
   *
   * @apiErrorExample {json} BadRequest
   * {
   *   "error": "NoSuchService",
   *   "message": "We do not recognise this service id"
   * }
   * @apiSuccessExample {json} Service:
   * HTTP/1.1 201 Created
   * {
   *   "id": 24536,
   *   "clientId": 1,
   *   "client": "rubiz",
   *   "username": "1d7341d729",
   *   "password": "134128b1a6",
   *   "status": 0,
   *   "uuid": "6b4025fb-9b85-4626-90db-e34ed7bb688b",
   *   "quota": "[{\"traffic\":************,\"speed\":3932160}]",
   *   "type": 1,
   *   "upload": "0",
   *   "download": "0",
   *   "resetDate": "2022-04-08T14:47:59.000Z",
   *   "updatedAt": "1969-12-31T16:00:00.000Z",
   *   "expiredAt": "2022-07-08T00:00:00.000Z",
   *   "speedLimit": 0
   * }
   */
  @Post('show')
  // @UseGuards(AuthGuard)
  async show(@Body() body: ResetDTO) {

    const services = await Service.find({ where: {client: body.client, clientId: body.clientId} });
    if (services.length === 0) {
      throw new HttpException({
        error: 'NoSuchService',
        message: 'We do not recognise this service id',
      }, 404);
    }
    const service = services[0];
    return service.toJson();
  }

  /**
   * @api {get} /api/v1/services/:client/:clientId/subscription/direct Get service subscription (direct)
   * @apiName GetServiceSubscription(Direct)
   * @apiGroup Service
   *
   * @apiParam {Number} clientId user's serviceId in whmcs.
   * @apiParam {String="jetstream", "jetstream-uat", "flash", "flash-uat", "rubiz", "rubiz-uat", "lightning", "lightning-uat"} client product name.
   * @apiQuery {String="none", "clash", "surge", "shadowrocket", "quantumult", "v2rayu", "switchyomega", "quantumultx", "jetstream", "leaf"} agent agent name.
   * @apiQuery {String="yes"} allShards whether to use all shards, not working for direct.
   * @apiQuery {String="yes"} binary whether to generate a configuration for binary files, only valid for leaf
   * @apiQuery {String="yes"} isSelfDevClient Whether to return the official website and service usage information in the configuration file, Only valid for clash
   *
   * @apiDescription <h2>为了节点安全，不要在国内使用直连订阅！！！</h2>For node security, do not use direct connection subscriptions in China! ! !<br /> For different parameters, the returned configuration is also different. For details, you can refer to <a href="https://www.notion.so/foxit/f1fe76c2f69c4073b98419c3cb1d74c7" target="_blank">Superapi</a> section. This API will be deprecated in a future version, please use <code>/api/v1/services/:client/:clientId/subscription</code>
   */
  @Get(':product/:id/subscription/direct')
  async directSubscription(@Param() params, @Query() query: SubscriptionV2DTO, @Res() res, @Req() req) {
    query.direct = 'yes';
    return this.subscriptionV2(params, query, res, req);
  }

  /**
   * @api {get} /api/v1/services/:client/:clientId/subscription Get service subscription
   * @apiName GetServiceSubscription
   * @apiGroup Service
   *
   * @apiParam {Number} clientId user's serviceId in whmcs.
   * @apiParam {String="jetstream", "jetstream-uat", "flash", "flash-uat", "rubiz", "rubiz-uat", "lightning", "lightning-uat"} client product name.
   * @apiQuery {String="none", "clash", "surge", "shadowrocket", "quantumult", "v2rayu", "switchyomega", "quantumultx", "jetstream", "leaf"} agent agent name.
   * @apiQuery {String="yes"} allShards whether to use all shards, not working for direct.
   * @apiQuery {String="yes"} binary whether to generate a configuration for binary files, only valid for leaf
   * @apiQuery {String="yes"} isSelfDevClient Whether to return the official website and service usage information in the configuration file, Only valid for clash
   * @apiQuery {String="yes"} direct Use a direct proxy, If you use the direct connection function, the grouping will not work.Do not use direct proxy in China!!!
   *
   * @apiDescription  For different parameters, the returned configuration is also different. For details, you can refer to <a href="https://www.notion.so/foxit/f1fe76c2f69c4073b98419c3cb1d74c7" target="_blank">Superapi</a> section.
   */
  @Get(':client/:id/subscription')
  async subscription(@Param() params, @Query() query: SubscriptionV2DTO, @Res() res, @Req() req) {
    await this.subscriptionV2(params, query, res, req);
  }

  /**
   * @api {get} /api/v1/services/:client/:clientId/subscriptionV2 Get service subscription v2
   * @apiName GetServiceSubscriptionV2
   * @apiGroup Service
   *
   * @apiParam {Number} clientId user's serviceId in whmcs.
   * @apiParam {String="jetstream", "jetstream-uat", "flash", "flash-uat", "rubiz", "rubiz-uat", "lightning", "lightning-uat"} client product name.
   * @apiQuery {String="none", "clash", "surge", "shadowrocket", "quantumult", "v2rayu", "switchyomega", "quantumultx", "jetstream", "leaf"} agent agent name.
   * @apiQuery {String="yes"} allShards whether to use all shards, not working for direct.
   * @apiQuery {String="yes"} binary whether to generate a configuration for binary files, only valid for leaf
   * @apiQuery {String="yes"} isSelfDevClient Whether to return the official website and service usage information in the configuration file, Only valid for clash
   * @apiQuery {String="yes"} direct Use a direct proxy, If you use the direct connection function, the grouping will not work.Do not use direct proxy in China!!!
   *
   * @apiDescription  For different parameters, the returned configuration is also different. For details, you can refer to <a href="https://www.notion.so/foxit/f1fe76c2f69c4073b98419c3cb1d74c7" target="_blank">Superapi</a> section.
   */
  @Get(':client/:id/subscriptionV2')
  async subscriptionV2(@Param() params, @Query() query: SubscriptionV2DTO, @Res() res, @Req() req) {

    this.logger.log(`serve subscription(v2) for ${JSON.stringify(params)} and ${JSON.stringify(query)}`);
    const service = await this.serviceService.getService(params.id, params.client);

    // enable all shards till we roll out apps
    let allShards = query.allShards === 'yes';
    // const allShards = true;
    if (params.client === 'ztnet') {
      allShards = true;
    }

    const binary = query.binary === 'yes';
    const isSelfDevClient = query.isSelfDevClient === 'yes';
    const direct = query.direct === 'yes';

    let nodes;
    nodes = this.nodesService.shardNodesV2(service.id, direct, allShards);

    switch (params.client) {
      case 'ztnet':
        nodes = nodes.filter(node => node.name.includes('HOME'));
        break;
    }
    // locales hold on for a while, as users need to adapt to the new naming

    // if (!query.project ||  (query.project && !query.project.includes('rubiz'))) {
    //   nodes = nodes.map(node => ({
    //     ...node,
    //     name: this.nodesService.translateNodeNameToChinese(node),
    //   }));
    // } else {
    //   nodes = nodes.filter(node => node.project.includes('rubiz'));
    //   req.lang = 'en_US';
    // }
    const result = this.proxyService.generateConfig(query.agent, nodes, service, query.lang, params.client, binary, isSelfDevClient, query.region, query.protocol, query.country);
    this.serviceService.setMIMEAndHeader(res, query.agent);
    res.status(200).send(result);
  }

  @Post('reset')
  // @UseGuards(AuthGuard)
  async resetTraffic(@Body() body: ResetDTO) {
    const services = await Service.find({ where: { clientId: body.clientId, client: body.client} });
    if (services.length !== 0) {
      const service = services[0];
      service.reset();
      await service.save();
      this.logger.log(`reset service traffic for ${JSON.stringify(body)}`);
      this.serviceService.syncServices(service);

      // 异步处理服务重置时的每日用量
      setImmediate(async () => {
        try {
          await this.dailyUsageService.handleServiceReset(service);
        } catch (error) {
          this.logger.error(`Failed to handle service reset for daily usage of service ${service.id}: ${error.message}`);
        }
      });

      return service.toJson();
    }
    return '';
  }

  /**
   * @api {post} /api/v1/services/dailyUsage Get daily usage statistics
   * @apiName GetDailyUsage
   * @apiGroup Service
   *
   * @apiBody {Number} clientId user's serviceId in whmcs.
   * @apiBody {String="jetstream", "jetstream-uat", "flash", "flash-uat", "rubiz", "rubiz-uat", "lightning", "lightning-uat"} client product name.
   * @apiBody {String} [startDate] start date in YYYY-MM-DD format.
   * @apiBody {String} [endDate] end date in YYYY-MM-DD format.
   *
   * @apiErrorExample {json} NoSuchService
   *  HTTP/1.1 400 Bad Request
   *  {
   *    "error": "NoSuchService",
   *    "message": "We do not have this service"
   *  }
   *
   * @apiSuccessExample {json} Success
   *  HTTP/1.1 200 OK
   *  [
   *    {
   *      "id": 1,
   *      "clientId": 123,
   *      "client": "rubiz",
   *      "date": "2025-01-11",
   *      "upload": "1048576",
   *      "download": "2097152",
   *      "total": "3145728",
   *      "createdAt": "2025-01-11T00:00:00.000Z",
   *      "updatedAt": "2025-01-11T12:30:00.000Z"
   *    }
   *  ]
   */
  @Post('dailyUsage')
  // @UseGuards(AuthGuard)
  async getDailyUsage(@Body() body: DailyUsageQueryDTO) {
    try {
      // 验证服务是否存在
      const service = await this.serviceService.getService(body.clientId.toString(), body.client);

      const dailyUsageRecords = await this.dailyUsageService.getDailyUsage(
        body.clientId,
        body.client,
        body.startDate,
        body.endDate
      );

      return dailyUsageRecords.map(record => record.toJson());
    } catch (error) {
      if (error.status === 404) {
        throw error;
      }
      this.logger.error(`Failed to get daily usage for ${body.client}:${body.clientId}: ${error.message}`);
      throw new HttpException({
        error: 'FailedToGetDailyUsage',
        message: 'Failed to retrieve daily usage statistics',
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * @api {post} /api/v1/services/usageSummary Get usage summary statistics
   * @apiName GetUsageSummary
   * @apiGroup Service
   *
   * @apiBody {Number} clientId user's serviceId in whmcs.
   * @apiBody {String="jetstream", "jetstream-uat", "flash", "flash-uat", "rubiz", "rubiz-uat", "lightning", "lightning-uat"} client product name.
   * @apiBody {String} [startDate] start date in YYYY-MM-DD format.
   * @apiBody {String} [endDate] end date in YYYY-MM-DD format.
   *
   * @apiErrorExample {json} NoSuchService
   *  HTTP/1.1 400 Bad Request
   *  {
   *    "error": "NoSuchService",
   *    "message": "We do not have this service"
   *  }
   *
   * @apiSuccessExample {json} Success
   *  HTTP/1.1 200 OK
   *  {
   *    "days": 30,
   *    "totalUpload": "104857600",
   *    "totalDownload": "209715200",
   *    "totalUsage": "314572800",
   *    "avgDailyUsage": "10485760",
   *    "maxDailyUsage": "52428800",
   *    "minDailyUsage": "1048576"
   *  }
   */
  @Post('usageSummary')
  // @UseGuards(AuthGuard)
  async getUsageSummary(@Body() body: DailyUsageQueryDTO) {
    try {
      // 验证服务是否存在
      const service = await this.serviceService.getService(body.clientId.toString(), body.client);

      const summary = await this.dailyUsageService.getUsageSummary(
        body.clientId,
        body.client,
        body.startDate,
        body.endDate
      );

      return summary;
    } catch (error) {
      if (error.status === 404) {
        throw error;
      }
      this.logger.error(`Failed to get usage summary for ${body.client}:${body.clientId}: ${error.message}`);
      throw new HttpException({
        error: 'FailedToGetUsageSummary',
        message: 'Failed to retrieve usage summary statistics',
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * @api {get} /api/v1/services/performance Get performance metrics
   * @apiName GetPerformanceMetrics
   * @apiGroup Service
   *
   * @apiSuccessExample {json} Success
   *  HTTP/1.1 200 OK
   *  {
   *    "current": {
   *      "timestamp": "2025-01-11T12:00:00.000Z",
   *      "memoryUsage": {...},
   *      "cpuUsage": 25.5,
   *      "requestsPerSecond": 150.2,
   *      "averageResponseTime": 45.8,
   *      "dailyUsageOperations": {...}
   *    },
   *    "issues": ["High memory usage: 512.5MB"],
   *    "historical": [...]
   *  }
   */
  @Get('performance')
  async getPerformanceMetrics() {
    try {
      const current = this.performanceMonitor.getCurrentMetrics();
      const issues = this.performanceMonitor.checkPerformanceIssues();
      const historical = this.performanceMonitor.getHistoricalMetrics(10);

      return {
        current,
        issues,
        historical,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to get performance metrics: ${error.message}`);
      throw new HttpException({
        error: 'FailedToGetPerformanceMetrics',
        message: 'Failed to retrieve performance metrics',
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
