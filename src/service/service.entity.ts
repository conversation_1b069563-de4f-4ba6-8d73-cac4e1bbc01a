import { Entity, Column, PrimaryGeneratedColumn, BaseEntity, EntityRepository, Repository, Unique } from 'typeorm';
import * as Big from 'big.js';
import * as crypto from 'crypto';
import * as moment from 'moment';
import { ServiceService } from './service.service';

export enum ServiceStatus {
  Suspended = 0,
  Active = 1,
}

export enum ServiceResetType {
  Never = 0,
  Monthly = 1,
}

@Entity('services')
@Unique(['client', 'clientId'])
export class Service  extends BaseEntity  {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({type: 'varchar', width: 32, name: 'client', nullable: false})
  client: string;

  @Column({type: 'int', width: 10, name: 'client_id', nullable: false})
  clientId: number;

  @Column({type: 'varchar', width: 32, nullable: false})
  username: string;

  @Column({type: 'varchar', width: 128, nullable: false})
  password: string;

  @Column({type: 'varchar', width: 128, nullable: false, unique: true})
  uuid: string;

  @Column({type: 'datetime', name: 'last_updated_time', nullable: true, default: '1970-01-01 00:00:00'})
  lastUpdatedTime: Date;

  @Column({type: 'text', name: 'traffic_quota', nullable: false})
  trafficQuota: string;

  @Column({type: 'bigint', width: 20, name: 'traffic_upload', nullable: false, default: 0 })
  trafficUpload: number;

  @Column({type: 'bigint', width: 20, name: 'traffic_download', nullable: false, default: 0 })
  trafficDownload: number;

  @Column({type: 'int', width: 10, name: 'connection_limit', nullable: true, default: 1})
  connectionLimit: number;

  @Column({type: 'tinyint', width: 4, nullable: false, default: ServiceStatus.Suspended})
  status: ServiceStatus;

  @Column({type: 'tinyint', width: 4, name: 'traffic_reset_cycle' , nullable: false, default: ServiceResetType.Never})
  trafficResetCycle: ServiceResetType;

  @Column({type: 'datetime', name: 'traffic_next_reset_date', nullable: false, default: '1970-01-01 00:00:00' })
  trafficNextResetDate: Date;

  @Column({type: 'datetime',  name: 'expired_at', nullable: false, default: '1970-01-01 00:00:00' })
  expiredAt: Date;

  toJson() {
    return {
      id: this.id,
      clientId: this.clientId,
      client: this.client,
      username: this.username,
      password: this.password,
      status: this.status,
      uuid: this.uuid,
      quota: this.getLatestQuota(),
      type: this.trafficResetCycle,
      upload: `${this.trafficUpload}`,
      download: `${this.trafficDownload}`,
      resetDate: this.trafficNextResetDate,
      updatedAt: this.lastUpdatedTime,
      expiredAt: this.expiredAt,
      speedLimit: this.speedLimit(),
    };
  }

  getLatestQuota() {
    const quota = JSON.parse(this.trafficQuota);
    const used = (new Big(this.trafficUpload)).plus(new Big(this.trafficDownload));
    const percent = quota[0].traffic > 0 ? used.div(quota[0].traffic).toFixed(2) : 1;
    if (percent < 1.5) {
      return this.trafficQuota;
    }
    if (percent >= 2.5) {
      return JSON.stringify([{
        traffic: quota[0].traffic,
        speed: 800, // 100KB/s
      }]);
    }
    if (percent >= 2) {
      return JSON.stringify([{
        traffic: quota[0].traffic,
        speed: Math.floor(quota[0].speed / 4),
      }]);
    }
    if (percent >= 1.5) {
      return JSON.stringify([{
        traffic: quota[0].traffic,
        speed: Math.floor(quota[0].speed / 2),
      }]);
    }
  }

  /**
   *  Get a token used to authenticate against current service
   */
  token() {
    return crypto.createHash('md5').update(this.password).digest('hex');
  }

  daysTillDue(): number {
    return moment.duration(moment.utc(this.expiredAt).diff(moment.utc())).days();
  }

  isUsedUp(): boolean {
    return this.speedLimit() !== -1;
  }
  isDue(): boolean {

    // never due for one time
    if (this.trafficResetCycle === ServiceResetType.Never) {
      return false;
    }
    return this.expiredAt < moment.utc().toDate();
  }
  /**
   * Calculate current speed limit
   */
  speedLimit(): number {
    // if due, then limit it to 0
    if (this.isDue()) {
      return 0;
    }

    // default -1 means no speed limit control
    let speedLimit = -1;

    // if service is somehow not activated, limit it to 0
    if (this.status !== ServiceStatus.Active) {
      speedLimit = 0;
    } else {
      // calculate all usage in Unit Byte
      const currentUsage = (new Big(this.trafficUpload)).plus(new Big(this.trafficDownload));

      const quote = JSON.parse(this.trafficQuota);

      // found corresponding tier
      const allowed = new Big(quote[0].traffic);
      const limit = parseInt(quote[0].speed) || 0;

      if (currentUsage.gte(allowed)) {
        if (this.trafficResetCycle === ServiceResetType.Monthly) {
          const diff = currentUsage.minus(allowed);
          speedLimit = 1310720;
          //  used 2G more, 1.8mbps  is enough for 480p video
          if (diff.gte(new Big(2147483648))) {
            speedLimit = 786432;
          }
          //  used 4G more, 1.3mbps  is enough for 480p video
          if (diff.gte(new Big(4294967296))) {
            speedLimit = 655360;
          }
          //  used 6G more, 800kbps  is enough for 360p video
          if (diff.gte(new Big(6442450944))) {
            speedLimit = 524288;
          }
          //  used 8G more, 300kbps  is enough for 240p video
          if (diff.gte(new Big(8589934592))) {
            speedLimit = 393216;
          }
          //  used 10G more, 200kbps is just enough for text browsing
          if (diff.gte(new Big(10737418240))) {
            speedLimit = 262144;
          }
        } else {
          // for one time service, it should be 0
          speedLimit = 0;
        }
          
        //  if the calulated speedLimit is higher than the limit defined in quote, use the limit
        if (speedLimit > limit) {
          speedLimit = limit;
        }
      }
    }

    return speedLimit;
  }

  reduceUsage(delta: number) {

    if (delta < 1) {
      return;
    }

    if (this.trafficDownload >= delta) {
      this.trafficDownload -= delta;
      return;
    }

    this.trafficDownload = 0;
    const remained = delta - this.trafficDownload;

    if (this.trafficUpload >= remained) {
      this.trafficUpload -= remained;
      return;
    }
    this.trafficUpload = 0;
  }

  updateQuota(delta: number) {
    const quote = JSON.parse(this.trafficQuota);
    for (const q of quote) {
      let allowed = new Big(q.traffic);
      allowed =  allowed.plus(delta);
      q.traffic = Number(allowed.toFixed(0));
    }
    this.trafficQuota = JSON.stringify(quote);
  }

  // reset data according to expiration and reset date
  reset() {
    if (this.trafficResetCycle) {
      const quote = JSON.parse(this.trafficQuota);
      switch (quote[0].speed) {
        case 2621440:
          this.trafficQuota = JSON.stringify([{
            traffic: 107374182400,
            speed: 2621440,
          }]);
          break;
        case 3932160:
          this.trafficQuota = JSON.stringify([{
            traffic: 214748364800,
            speed: 3932160,
          }]);
          break;
        default:
          this.trafficQuota = JSON.stringify([{
            traffic: 53687091200,
            speed: 1310720,
          }]);
      }
      this.trafficUpload = 0;
      this.trafficDownload = 0;
      this.trafficNextResetDate = ServiceService.getNextResetDate(moment.utc(this.expiredAt).format('YYYY-MM-DD'));
    }
  }
}
