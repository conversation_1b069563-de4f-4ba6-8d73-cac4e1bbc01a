import { Injectable, Logger, Inject, forwardRef, OnModuleInit } from '@nestjs/common';
import { DailyUsage } from './daily-usage.entity';
import { Service } from './service.entity';
import * as moment from 'moment';
import * as schedule from 'node-schedule';
import { getRepository } from 'typeorm';

@Injectable()
export class DailyUsageService implements OnModuleInit {
  private readonly logger = new Logger(DailyUsageService.name);

  onModuleInit() {
    // 每天凌晨2点清理90天前的记录
    schedule.scheduleJob('0 0 2 * * *', async () => {
      await this.cleanupOldRecords(90);
    });
  }

  /**
   * 记录或更新每日用量
   * @param service 服务实例
   * @param uploadDelta 上传增量
   * @param downloadDelta 下载增量
   */
  async recordDailyUsage(service: Service, uploadDelta: number, downloadDelta: number): Promise<DailyUsage> {
    const today = moment.utc().format('YYYY-MM-DD');
    
    try {
      // 查找今日的用量记录
      let dailyUsage = await DailyUsage.findOne({
        where: {
          clientId: service.clientId,
          client: service.client,
          date: today,
        },
      });

      if (dailyUsage) {
        // 更新现有记录
        dailyUsage.updateUsage(uploadDelta, downloadDelta);
        this.logger.log(`Updated daily usage for client ${service.client}:${service.clientId} on ${today}, upload: +${uploadDelta}, download: +${downloadDelta}`);
      } else {
        // 创建新记录
        dailyUsage = new DailyUsage();
        dailyUsage.clientId = service.clientId;
        dailyUsage.client = service.client;
        dailyUsage.date = new Date(today);
        dailyUsage.updateUsage(uploadDelta, downloadDelta);
        this.logger.log(`Created daily usage record for client ${service.client}:${service.clientId} on ${today}, upload: ${uploadDelta}, download: ${downloadDelta}`);
      }

      await dailyUsage.save();
      return dailyUsage;
    } catch (error) {
      this.logger.error(`Failed to record daily usage for client ${service.client}:${service.clientId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 处理服务重置时的每日用量
   * @param service 服务实例
   */
  async handleServiceReset(service: Service): Promise<void> {
    const today = moment.utc().format('YYYY-MM-DD');
    
    try {
      // 查找今日的用量记录
      const dailyUsage = await DailyUsage.findOne({
        where: {
          clientId: service.clientId,
          client: service.client,
          date: today,
        },
      });

      if (dailyUsage) {
        // 重置今日用量记录
        dailyUsage.reset();
        await dailyUsage.save();
        this.logger.log(`Reset daily usage for client ${service.client}:${service.clientId} on ${today} due to service reset`);
      }
    } catch (error) {
      this.logger.error(`Failed to handle service reset for daily usage of client ${service.client}:${service.clientId}: ${error.message}`);
    }
  }

  /**
   * 获取指定用户的每日用量统计
   * @param clientId 客户端ID
   * @param client 客户端名称
   * @param startDate 开始日期 (YYYY-MM-DD)
   * @param endDate 结束日期 (YYYY-MM-DD)
   */
  async getDailyUsage(clientId: number, client: string, startDate?: string, endDate?: string): Promise<DailyUsage[]> {
    try {
      const queryBuilder = getRepository(DailyUsage)
        .createQueryBuilder('daily_usage')
        .where('daily_usage.clientId = :clientId', { clientId })
        .andWhere('daily_usage.client = :client', { client });

      if (startDate) {
        queryBuilder.andWhere('daily_usage.date >= :startDate', { startDate });
      }

      if (endDate) {
        queryBuilder.andWhere('daily_usage.date <= :endDate', { endDate });
      }

      const results = await queryBuilder
        .orderBy('daily_usage.date', 'DESC')
        .getMany();

      return results;
    } catch (error) {
      this.logger.error(`Failed to get daily usage for client ${client}:${clientId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取指定日期范围内的用量统计汇总
   * @param clientId 客户端ID
   * @param client 客户端名称
   * @param startDate 开始日期 (YYYY-MM-DD)
   * @param endDate 结束日期 (YYYY-MM-DD)
   */
  async getUsageSummary(clientId: number, client: string, startDate?: string, endDate?: string): Promise<any> {
    try {
      const queryBuilder = getRepository(DailyUsage)
        .createQueryBuilder('daily_usage')
        .select([
          'COUNT(*) as days',
          'SUM(daily_usage.upload) as totalUpload',
          'SUM(daily_usage.download) as totalDownload',
          'SUM(daily_usage.total) as totalUsage',
          'AVG(daily_usage.total) as avgDailyUsage',
          'MAX(daily_usage.total) as maxDailyUsage',
          'MIN(daily_usage.total) as minDailyUsage',
        ])
        .where('daily_usage.clientId = :clientId', { clientId })
        .andWhere('daily_usage.client = :client', { client });

      if (startDate) {
        queryBuilder.andWhere('daily_usage.date >= :startDate', { startDate });
      }

      if (endDate) {
        queryBuilder.andWhere('daily_usage.date <= :endDate', { endDate });
      }

      const result = await queryBuilder.getRawOne();
      
      return {
        days: parseInt(result.days) || 0,
        totalUpload: result.totalUpload || '0',
        totalDownload: result.totalDownload || '0',
        totalUsage: result.totalUsage || '0',
        avgDailyUsage: result.avgDailyUsage || '0',
        maxDailyUsage: result.maxDailyUsage || '0',
        minDailyUsage: result.minDailyUsage || '0',
      };
    } catch (error) {
      this.logger.error(`Failed to get usage summary for client ${client}:${clientId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 清理过期的每日用量记录（保留指定天数）
   * @param retentionDays 保留天数，默认90天
   */
  async cleanupOldRecords(retentionDays: number = 90): Promise<void> {
    try {
      const cutoffDate = moment.utc().subtract(retentionDays, 'days').format('YYYY-MM-DD');
      
      const result = await getRepository(DailyUsage)
        .createQueryBuilder()
        .delete()
        .from(DailyUsage)
        .where('date < :cutoffDate', { cutoffDate })
        .execute();

      this.logger.log(`Cleaned up ${result.affected} old daily usage records older than ${cutoffDate}`);
    } catch (error) {
      this.logger.error(`Failed to cleanup old daily usage records: ${error.message}`);
    }
  }
}
