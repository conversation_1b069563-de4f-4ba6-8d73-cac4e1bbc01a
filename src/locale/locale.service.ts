import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import { mergeMap } from 'rxjs/operators';
import * as agent from 'superagent';
import { Firestore } from '@google-cloud/firestore';
import * as Rx from 'rxjs';
import { Subject } from 'rxjs';
import { existsSync, readFileSync } from 'fs';
import * as crypto from 'crypto';
import * as Handlebars from 'handlebars';

@Injectable()
export class LocaleService implements OnModuleInit {

  private readonly logger = new Logger(LocaleService.name);
  public translations = {};
  private firestore: Firestore;
  private $local = new Subject();

  constructor() {
    this.firestore = new Firestore({
      projectId: 'aquadata-efa8e',
      keyFilename: path.resolve(__filename, '../../../firebase.json'),
    });
  }

  onModuleInit(): any {
    this.start18n();
  }

  /**
   * find the translation of a key
   * @param locale
   * @param key
   */
  public translate(locale, key): string{
    return this.translations[locale][key];
  }

  public translateTemplate(locale, templateName, data) {
    const source   = path.join(__filename, `../templates/${templateName}.html`);
    if (!existsSync(source)) {
      throw new Error(`Can not find template ${templateName} at location ${source}`);
    }
    const template = Handlebars.compile(readFileSync(source).toString());
    return template({...data, ...this.translations[locale]});
  }
  // sync 18n
  private start18n() {
    // this.$local
    //   .pipe(mergeMap(language => {
    //     const $ = agent.post('https://api.poeditor.com/v2/projects/export')
    //       .type('form')
    //       .send({api_token: 'f0ef1e4affe2b90488b7e11de778132e', id: '289989', language, type: 'key_value_json'}).then();
    //     return  Rx.from($);
    //   }))
    //   .subscribe(result => {
    //     try {
    //
    //       const language = result['request']._data.language;
    //       const tmpfile = `/tmp/i18n-${crypto.randomBytes(6).toString('hex')}-${language}.json`;
    //       const stream = fs.createWriteStream(tmpfile);
    //       const req = agent.get(result.body.result.url);
    //       req.pipe(stream);
    //       req.on('end', () => {
    //         try {
    //           this.translations[language] = JSON.parse(fs.readFileSync(path.resolve(tmpfile)).toString());
    //           this.logger.log(`i18n: finish reloading 18n files for ${language}`);
    //         } catch (e) {
    //           this.logger.log(`18n: failed to sync file: ${e}`);
    //         }
    //       });
    //     } catch (e) {
    //       this.logger.log(`18n: failed to sync file: ${e}`);
    //     }
    //   }, error => this.logger.log(`18n: failed to download: ${error}`) );

    ['en-US', 'zh-Hans', 'zh-Hant'].forEach(language => {
      this.translations[language] = JSON.parse(fs.readFileSync(path.resolve(__dirname, `./locales/${language}.json`)).toString());
      this.logger.log(`18n: load local locale file for ${language}`);
    });

    const doc = this.firestore.collection('i18n');
    const observer = doc.onSnapshot(docSnapshot => {
      Rx.from(docSnapshot.docChanges()).subscribe(change => this.$local.next(change.doc.data().language));
    }, err => {
      this.logger.log(`Firebase: Encountered error: ${err}`);
    });
  }
}
