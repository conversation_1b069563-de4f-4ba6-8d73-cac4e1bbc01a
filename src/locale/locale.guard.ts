import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Observable } from 'rxjs';
import * as _ from 'lodash';

@Injectable()
export class LocaleGuard implements CanActivate {
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const req = context.switchToHttp().getRequest();
    let lang = '';
    if (req.headers['content-language'] === undefined || !_.includes(['en-US', 'zh-Hans', 'zh-Hant', 'ja', 'ru', 'es-ES'], req.headers['content-language'])) {
      lang = 'en-US';
    } else {
      lang = req.headers['content-language'];
    }
    req.lang = lang;
    return true;
  }
}
