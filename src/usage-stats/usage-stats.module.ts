import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DailyUsage } from './daily-usage.entity';
import { DailyUsageService } from './daily-usage.service';
import { MemoryCacheService } from './memory-cache.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([DailyUsage]),
  ],
  providers: [
    DailyUsageService,
    MemoryCacheService,
  ],
  exports: [
    DailyUsageService,
    MemoryCacheService,
  ],
})
export class UsageStatsModule {}
