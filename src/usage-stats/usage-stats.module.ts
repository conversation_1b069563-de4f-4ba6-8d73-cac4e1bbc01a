import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DailyUsage } from './entities/daily-usage.entity';
import { DailyUsageService } from './services/daily-usage.service';
import { MemoryCacheService } from './services/memory-cache.service';
import { UsageStatsController } from './controllers/usage-stats.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([DailyUsage]),
  ],
  controllers: [UsageStatsController],
  providers: [
    DailyUsageService,
    MemoryCacheService,
  ],
  exports: [
    DailyUsageService,
    MemoryCacheService,
  ],
})
export class UsageStatsModule {}
