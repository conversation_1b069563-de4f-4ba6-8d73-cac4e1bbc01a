import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { DailyUsage } from '../entities/daily-usage.entity';
import { MemoryCacheService } from './memory-cache.service';
import * as moment from 'moment';
import * as schedule from 'node-schedule';
import { getRepository } from 'typeorm';

@Injectable()
export class DailyUsageService implements OnModuleInit {
  private readonly logger = new Logger(DailyUsageService.name);
  private readonly BATCH_SIZE = 100;

  constructor(
    private memoryCache: MemoryCacheService,
  ) {}

  onModuleInit() {
    // 每天凌晨2点清理90天前的记录
    schedule.scheduleJob('0 0 2 * * *', async () => {
      await this.cleanupOldRecords(90);
    });

    // 每30秒批量刷新缓存到数据库
    schedule.scheduleJob('*/30 * * * * *', async () => {
      await this.flushCacheToDatabase();
    });

    // 每小时强制同步一次，确保数据一致性
    schedule.scheduleJob('0 * * * *', async () => {
      await this.forceSyncToDatabase();
    });
  }

  /**
   * 记录或更新每日用量（使用内存缓存优化性能）
   * @param clientId 客户端ID
   * @param client 客户端名称
   * @param uploadDelta 上传增量
   * @param downloadDelta 下载增量
   */
  async recordDailyUsage(clientId: number, client: string, uploadDelta: number, downloadDelta: number): Promise<void> {
    const today = moment.utc().format('YYYY-MM-DD');
    
    try {
      // 使用内存缓存原子性增加用量
      this.memoryCache.incrementUsage(client, clientId, today, uploadDelta, downloadDelta);

      // 异步记录日志，不阻塞主流程
      setImmediate(() => {
        // 使用log方法，在生产环境中可以通过日志级别控制
        if (process.env.NODE_ENV === 'development') {
          this.logger.log(`Cached daily usage for client ${client}:${clientId} on ${today}, upload: +${uploadDelta}, download: +${downloadDelta}`);
        }
      });
    } catch (error) {
      this.logger.error(`Failed to cache daily usage for client ${client}:${clientId}: ${error.message}`);
      // 缓存失败时回退到直接数据库操作
      await this.recordDailyUsageDirectly(clientId, client, uploadDelta, downloadDelta);
    }
  }

  /**
   * 直接写入数据库的备用方法
   */
  private async recordDailyUsageDirectly(clientId: number, client: string, uploadDelta: number, downloadDelta: number): Promise<void> {
    const today = moment.utc().format('YYYY-MM-DD');
    
    try {
      // 使用原生SQL的UPSERT操作提高性能
      await getRepository(DailyUsage).query(`
        INSERT INTO daily_usage (client_id, client, date, upload, download, total, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
        ON DUPLICATE KEY UPDATE
          upload = upload + VALUES(upload),
          download = download + VALUES(download),
          total = total + VALUES(total),
          updated_at = NOW()
      `, [clientId, client, today, uploadDelta, downloadDelta, uploadDelta + downloadDelta]);

      this.logger.log(`Direct DB update for client ${client}:${clientId} on ${today}`);
    } catch (error) {
      this.logger.error(`Failed to record daily usage directly for client ${client}:${clientId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 批量刷新缓存到数据库
   */
  private async flushCacheToDatabase(): Promise<void> {
    try {
      const allEntries = this.memoryCache.getAllEntries();
      
      if (allEntries.length === 0) {
        return;
      }

      this.logger.log(`Flushing ${allEntries.length} cached daily usage records to database`);
      
      // 批量处理，避免一次性处理太多数据
      for (let i = 0; i < allEntries.length; i += this.BATCH_SIZE) {
        const batch = allEntries.slice(i, i + this.BATCH_SIZE);
        await this.processBatch(batch);
      }
      
      this.logger.log(`Successfully flushed ${allEntries.length} records to database`);
    } catch (error) {
      this.logger.error(`Failed to flush cache to database: ${error.message}`);
    }
  }

  /**
   * 处理一批缓存记录
   */
  private async processBatch(entries: any[]): Promise<void> {
    if (entries.length === 0) {
      return;
    }

    // 批量写入数据库
    await this.batchUpsertToDatabase(entries);
    
    // 从内存缓存中删除已处理的条目
    entries.forEach(entry => {
      this.memoryCache.deleteEntry(entry.client, entry.clientId, entry.date);
    });
  }

  /**
   * 批量UPSERT到数据库
   */
  private async batchUpsertToDatabase(records: any[]): Promise<void> {
    if (records.length === 0) return;

    const values = records.map(record => 
      `(${record.clientId}, '${record.client}', '${record.date}', ${record.upload}, ${record.download}, ${record.total}, NOW(), NOW())`
    ).join(',');

    const sql = `
      INSERT INTO daily_usage (client_id, client, date, upload, download, total, created_at, updated_at)
      VALUES ${values}
      ON DUPLICATE KEY UPDATE
        upload = upload + VALUES(upload),
        download = download + VALUES(download),
        total = total + VALUES(total),
        updated_at = NOW()
    `;

    await getRepository(DailyUsage).query(sql);
  }

  /**
   * 强制同步所有缓存到数据库
   */
  private async forceSyncToDatabase(): Promise<void> {
    this.logger.log('Starting forced sync of all cached daily usage data');
    await this.flushCacheToDatabase();
    this.logger.log('Completed forced sync of cached daily usage data');
  }

  /**
   * 获取指定用户的每日用量统计（合并内存缓存和数据库数据）
   * @param clientId 客户端ID
   * @param client 客户端名称
   * @param startDate 开始日期 (YYYY-MM-DD)
   * @param endDate 结束日期 (YYYY-MM-DD)
   */
  async getDailyUsage(clientId: number, client: string, startDate?: string, endDate?: string): Promise<DailyUsage[]> {
    try {
      // 获取数据库中的记录
      const queryBuilder = getRepository(DailyUsage)
        .createQueryBuilder('daily_usage')
        .where('daily_usage.clientId = :clientId', { clientId })
        .andWhere('daily_usage.client = :client', { client });

      if (startDate) {
        queryBuilder.andWhere('daily_usage.date >= :startDate', { startDate });
      }

      if (endDate) {
        queryBuilder.andWhere('daily_usage.date <= :endDate', { endDate });
      }

      const dbResults = await queryBuilder
        .orderBy('daily_usage.date', 'DESC')
        .getMany();

      // 获取内存缓存中的今日数据
      const today = moment.utc().format('YYYY-MM-DD');
      
      try {
        const cachedEntry = this.memoryCache.getEntry(client, clientId, today);
        
        if (cachedEntry) {
          // 检查今日是否在查询范围内
          const todayInRange = (!startDate || today >= startDate) && (!endDate || today <= endDate);
          
          if (todayInRange) {
            // 查找数据库中是否已有今日记录
            const todayRecord = dbResults.find(record => 
              moment.utc(record.date).format('YYYY-MM-DD') === today
            );

            if (todayRecord) {
              // 合并缓存数据到现有记录
              todayRecord.upload += cachedEntry.upload;
              todayRecord.download += cachedEntry.download;
              todayRecord.total += cachedEntry.total;
            } else {
              // 创建新的今日记录
              const newRecord = new DailyUsage();
              newRecord.clientId = clientId;
              newRecord.client = client;
              newRecord.date = new Date(today);
              newRecord.upload = cachedEntry.upload;
              newRecord.download = cachedEntry.download;
              newRecord.total = cachedEntry.total;
              newRecord.createdAt = new Date();
              newRecord.updatedAt = new Date();
              
              dbResults.unshift(newRecord); // 添加到开头（最新日期）
            }
          }
        }
      } catch (cacheError) {
        this.logger.warn(`Failed to get cached data for ${client}:${clientId}: ${cacheError.message}`);
      }

      return dbResults;
    } catch (error) {
      this.logger.error(`Failed to get daily usage for client ${client}:${clientId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取指定日期范围内的用量统计汇总
   * @param clientId 客户端ID
   * @param client 客户端名称
   * @param startDate 开始日期 (YYYY-MM-DD)
   * @param endDate 结束日期 (YYYY-MM-DD)
   */
  async getUsageSummary(clientId: number, client: string, startDate?: string, endDate?: string): Promise<any> {
    try {
      const queryBuilder = getRepository(DailyUsage)
        .createQueryBuilder('daily_usage')
        .select([
          'COUNT(*) as days',
          'SUM(daily_usage.upload) as totalUpload',
          'SUM(daily_usage.download) as totalDownload',
          'SUM(daily_usage.total) as totalUsage',
          'AVG(daily_usage.total) as avgDailyUsage',
          'MAX(daily_usage.total) as maxDailyUsage',
          'MIN(daily_usage.total) as minDailyUsage',
        ])
        .where('daily_usage.clientId = :clientId', { clientId })
        .andWhere('daily_usage.client = :client', { client });

      if (startDate) {
        queryBuilder.andWhere('daily_usage.date >= :startDate', { startDate });
      }

      if (endDate) {
        queryBuilder.andWhere('daily_usage.date <= :endDate', { endDate });
      }

      const result = await queryBuilder.getRawOne();
      
      return {
        days: parseInt(result.days) || 0,
        totalUpload: result.totalUpload || '0',
        totalDownload: result.totalDownload || '0',
        totalUsage: result.totalUsage || '0',
        avgDailyUsage: result.avgDailyUsage || '0',
        maxDailyUsage: result.maxDailyUsage || '0',
        minDailyUsage: result.minDailyUsage || '0',
      };
    } catch (error) {
      this.logger.error(`Failed to get usage summary for client ${client}:${clientId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 清理过期的每日用量记录（保留指定天数）
   * @param retentionDays 保留天数，默认90天
   */
  async cleanupOldRecords(retentionDays: number = 90): Promise<void> {
    try {
      const cutoffDate = moment.utc().subtract(retentionDays, 'days').format('YYYY-MM-DD');
      
      const result = await getRepository(DailyUsage)
        .createQueryBuilder()
        .delete()
        .from(DailyUsage)
        .where('date < :cutoffDate', { cutoffDate })
        .execute();

      this.logger.log(`Cleaned up ${result.affected} old daily usage records older than ${cutoffDate}`);
    } catch (error) {
      this.logger.error(`Failed to cleanup old daily usage records: ${error.message}`);
    }
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return this.memoryCache.getStats();
  }
}
