import { Injectable, Logger, OnModuleInit } from '@nestjs/common';

interface CacheEntry {
  clientId: number;
  client: string;
  date: string;
  upload: number;
  download: number;
  total: number;
  lastUpdated: Date;
}

@Injectable()
export class MemoryCacheService implements OnModuleInit {
  private readonly logger = new Logger(MemoryCacheService.name);
  private cache = new Map<string, CacheEntry>();
  private readonly CACHE_PREFIX = 'daily_usage:';
  private readonly MAX_CACHE_SIZE = 10000; // 最大缓存条目数
  private readonly CACHE_TTL_HOURS = 48; // 缓存48小时

  onModuleInit() {
    // 每小时清理过期缓存
    setInterval(() => {
      this.cleanupExpiredEntries();
    }, 3600000); // 1小时

    this.logger.log('Memory cache service initialized');
  }

  /**
   * 获取缓存键
   */
  private getCacheKey(client: string, clientId: number, date: string): string {
    return `${this.CACHE_PREFIX}${client}:${clientId}:${date}`;
  }

  /**
   * 原子性增加用量
   */
  incrementUsage(client: string, clientId: number, date: string, uploadDelta: number, downloadDelta: number): void {
    const key = this.getCacheKey(client, clientId, date);
    const existing = this.cache.get(key);

    if (existing) {
      // 更新现有条目
      existing.upload += uploadDelta;
      existing.download += downloadDelta;
      existing.total += (uploadDelta + downloadDelta);
      existing.lastUpdated = new Date();
    } else {
      // 创建新条目
      const newEntry: CacheEntry = {
        clientId,
        client,
        date,
        upload: uploadDelta,
        download: downloadDelta,
        total: uploadDelta + downloadDelta,
        lastUpdated: new Date(),
      };
      this.cache.set(key, newEntry);
    }

    // 检查缓存大小限制
    this.checkCacheSize();
  }

  /**
   * 获取缓存条目
   */
  getEntry(client: string, clientId: number, date: string): CacheEntry | null {
    const key = this.getCacheKey(client, clientId, date);
    const entry = this.cache.get(key);
    
    if (entry && this.isEntryValid(entry)) {
      return { ...entry }; // 返回副本
    }
    
    return null;
  }

  /**
   * 删除缓存条目
   */
  deleteEntry(client: string, clientId: number, date: string): boolean {
    const key = this.getCacheKey(client, clientId, date);
    return this.cache.delete(key);
  }

  /**
   * 获取所有缓存条目
   */
  getAllEntries(): CacheEntry[] {
    const validEntries: CacheEntry[] = [];
    
    for (const [key, entry] of this.cache.entries()) {
      if (this.isEntryValid(entry)) {
        validEntries.push({ ...entry }); // 返回副本
      } else {
        // 删除过期条目
        this.cache.delete(key);
      }
    }
    
    return validEntries;
  }

  /**
   * 获取指定数量的条目用于批量处理
   */
  getBatchEntries(batchSize: number): CacheEntry[] {
    const entries = this.getAllEntries();
    const batch = entries.slice(0, batchSize);
    
    // 从缓存中删除已获取的条目
    batch.forEach(entry => {
      const key = this.getCacheKey(entry.client, entry.clientId, entry.date);
      this.cache.delete(key);
    });
    
    return batch;
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear();
    this.logger.log('Memory cache cleared');
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): {
    totalEntries: number;
    memoryUsage: string;
    oldestEntry: Date | null;
    newestEntry: Date | null;
  } {
    let oldestEntry: Date | null = null;
    let newestEntry: Date | null = null;

    for (const entry of this.cache.values()) {
      if (!oldestEntry || entry.lastUpdated < oldestEntry) {
        oldestEntry = entry.lastUpdated;
      }
      if (!newestEntry || entry.lastUpdated > newestEntry) {
        newestEntry = entry.lastUpdated;
      }
    }

    // 估算内存使用（粗略计算）
    const avgEntrySize = 200; // 字节
    const memoryBytes = this.cache.size * avgEntrySize;
    const memoryUsage = this.formatBytes(memoryBytes);

    return {
      totalEntries: this.cache.size,
      memoryUsage,
      oldestEntry,
      newestEntry,
    };
  }

  /**
   * 检查条目是否有效（未过期）
   */
  private isEntryValid(entry: CacheEntry): boolean {
    const now = new Date();
    const entryAge = now.getTime() - entry.lastUpdated.getTime();
    const maxAge = this.CACHE_TTL_HOURS * 60 * 60 * 1000; // 转换为毫秒
    
    return entryAge < maxAge;
  }

  /**
   * 检查缓存大小并清理最旧的条目
   */
  private checkCacheSize(): void {
    if (this.cache.size <= this.MAX_CACHE_SIZE) {
      return;
    }

    // 获取所有条目并按最后更新时间排序
    const entries = Array.from(this.cache.entries())
      .map(([key, entry]) => ({ key, entry }))
      .sort((a, b) => a.entry.lastUpdated.getTime() - b.entry.lastUpdated.getTime());

    // 删除最旧的条目，直到缓存大小在限制内
    const toDelete = this.cache.size - this.MAX_CACHE_SIZE + 100; // 多删除100个以避免频繁清理
    for (let i = 0; i < toDelete && i < entries.length; i++) {
      this.cache.delete(entries[i].key);
    }

    this.logger.warn(`Cache size exceeded limit. Removed ${toDelete} oldest entries. Current size: ${this.cache.size}`);
  }

  /**
   * 清理过期的缓存条目
   */
  private cleanupExpiredEntries(): void {
    const beforeSize = this.cache.size;
    let removedCount = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (!this.isEntryValid(entry)) {
        this.cache.delete(key);
        removedCount++;
      }
    }

    if (removedCount > 0) {
      this.logger.log(`Cleaned up ${removedCount} expired cache entries. Cache size: ${beforeSize} -> ${this.cache.size}`);
    }
  }

  /**
   * 格式化字节数为可读格式
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
