import { Controller, Post, Body, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { IsNotEmpty, IsOptional } from 'class-validator';
import { DailyUsageService } from '../services/daily-usage.service';

class DailyUsageQueryDTO {
  @IsNotEmpty()
  clientId: number;

  @IsNotEmpty()
  client: string;

  @IsOptional()
  startDate: string; // YYYY-MM-DD

  @IsOptional()
  endDate: string; // YYYY-MM-DD
}

@Controller('api/v1/usage-stats')
export class UsageStatsController {
  private readonly logger = new Logger(UsageStatsController.name);

  constructor(
    private dailyUsageService: DailyUsageService,
  ) {}

  /**
   * @api {post} /api/v1/usage-stats/daily Get daily usage statistics
   * @apiName GetDailyUsage
   * @apiGroup UsageStats
   *
   * @apiBody {Number} clientId user's serviceId in whmcs.
   * @apiBody {String="jetstream", "jetstream-uat", "flash", "flash-uat", "rubiz", "rubiz-uat", "lightning", "lightning-uat"} client product name.
   * @apiBody {String} [startDate] start date in YYYY-MM-DD format.
   * @apiBody {String} [endDate] end date in YYYY-MM-DD format.
   *
   * @apiErrorExample {json} ValidationError
   *  HTTP/1.1 400 Bad Request
   *  {
   *    "error": "ValidationError",
   *    "message": "Invalid request parameters"
   *  }
   *
   * @apiSuccessExample {json} Success
   *  HTTP/1.1 200 OK
   *  [
   *    {
   *      "id": 1,
   *      "clientId": 123,
   *      "client": "rubiz",
   *      "date": "2025-01-11",
   *      "upload": "1048576",
   *      "download": "2097152",
   *      "total": "3145728",
   *      "createdAt": "2025-01-11T00:00:00.000Z",
   *      "updatedAt": "2025-01-11T12:30:00.000Z"
   *    }
   *  ]
   */
  @Post('daily')
  async getDailyUsage(@Body() body: DailyUsageQueryDTO) {
    try {
      const dailyUsageRecords = await this.dailyUsageService.getDailyUsage(
        body.clientId,
        body.client,
        body.startDate,
        body.endDate
      );

      return dailyUsageRecords.map(record => record.toJson());
    } catch (error) {
      this.logger.error(`Failed to get daily usage for ${body.client}:${body.clientId}: ${error.message}`);
      throw new HttpException({
        error: 'FailedToGetDailyUsage',
        message: 'Failed to retrieve daily usage statistics',
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * @api {post} /api/v1/usage-stats/summary Get usage summary statistics
   * @apiName GetUsageSummary
   * @apiGroup UsageStats
   *
   * @apiBody {Number} clientId user's serviceId in whmcs.
   * @apiBody {String="jetstream", "jetstream-uat", "flash", "flash-uat", "rubiz", "rubiz-uat", "lightning", "lightning-uat"} client product name.
   * @apiBody {String} [startDate] start date in YYYY-MM-DD format.
   * @apiBody {String} [endDate] end date in YYYY-MM-DD format.
   *
   * @apiErrorExample {json} ValidationError
   *  HTTP/1.1 400 Bad Request
   *  {
   *    "error": "ValidationError",
   *    "message": "Invalid request parameters"
   *  }
   *
   * @apiSuccessExample {json} Success
   *  HTTP/1.1 200 OK
   *  {
   *    "days": 30,
   *    "totalUpload": "104857600",
   *    "totalDownload": "209715200",
   *    "totalUsage": "314572800",
   *    "avgDailyUsage": "10485760",
   *    "maxDailyUsage": "52428800",
   *    "minDailyUsage": "1048576"
   *  }
   */
  @Post('summary')
  async getUsageSummary(@Body() body: DailyUsageQueryDTO) {
    try {
      const summary = await this.dailyUsageService.getUsageSummary(
        body.clientId,
        body.client,
        body.startDate,
        body.endDate
      );

      return summary;
    } catch (error) {
      this.logger.error(`Failed to get usage summary for ${body.client}:${body.clientId}: ${error.message}`);
      throw new HttpException({
        error: 'FailedToGetUsageSummary',
        message: 'Failed to retrieve usage summary statistics',
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * @api {post} /api/v1/usage-stats/cache-stats Get cache statistics
   * @apiName GetCacheStats
   * @apiGroup UsageStats
   *
   * @apiSuccessExample {json} Success
   *  HTTP/1.1 200 OK
   *  {
   *    "totalEntries": 1250,
   *    "memoryUsage": "2.5 MB",
   *    "oldestEntry": "2025-01-11T10:00:00.000Z",
   *    "newestEntry": "2025-01-11T12:30:00.000Z"
   *  }
   */
  @Post('cache-stats')
  async getCacheStats() {
    try {
      const stats = this.dailyUsageService.getCacheStats();
      return stats;
    } catch (error) {
      this.logger.error(`Failed to get cache stats: ${error.message}`);
      throw new HttpException({
        error: 'FailedToGetCacheStats',
        message: 'Failed to retrieve cache statistics',
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
