# Usage Stats Module

## 概述

`usage-stats` 模块是一个独立的用量统计模块，负责处理每日用量的记录、查询和管理。该模块采用模块化设计，具有清晰的职责分离和良好的可维护性。

## 模块结构

```
src/usage-stats/
├── entities/
│   └── daily-usage.entity.ts          # 每日用量实体
├── services/
│   ├── daily-usage.service.ts         # 每日用量业务逻辑
│   └── memory-cache.service.ts        # 内存缓存服务
├── usage-stats.module.ts              # 模块定义
├── index.ts                           # 模块导出
└── README.md                          # 模块文档
```

## 核心组件

### 1. DailyUsage Entity
每日用量数据实体，包含以下字段：
- `id`: 主键
- `clientId`: 客户端ID
- `client`: 客户端名称
- `date`: 日期
- `upload`: 上传流量
- `download`: 下载流量
- `total`: 总流量
- `createdAt`: 创建时间
- `updatedAt`: 更新时间

### 2. MemoryCacheService
内存缓存服务，提供：
- 原子性用量增加操作
- 缓存条目管理
- 自动过期清理
- 大小限制控制
- 统计信息获取

### 3. DailyUsageService
每日用量业务逻辑服务，提供：
- 用量记录和更新
- 批量数据库同步
- 数据查询和汇总
- 缓存管理
- 数据清理



## API 接口

API接口由 `src/service/services.controller.ts` 提供：

### 1. 获取每日用量
```http
POST /api/v1/services/dailyUsage
Content-Type: application/json

{
  "clientId": 123,
  "client": "rubiz",
  "startDate": "2025-01-01",  // 可选
  "endDate": "2025-01-31"     // 可选
}
```

### 2. 获取用量汇总
```http
POST /api/v1/services/usageSummary
Content-Type: application/json

{
  "clientId": 123,
  "client": "rubiz",
  "startDate": "2025-01-01",  // 可选
  "endDate": "2025-01-31"     // 可选
}
```

详细的API文档请参考 `services.controller.ts` 中的注释。

## 使用方法

### 1. 在其他模块中使用

```typescript
import { UsageStatsModule, DailyUsageService } from '../usage-stats';

@Module({
  imports: [UsageStatsModule],
  // ...
})
export class YourModule {}
```

### 2. 记录用量数据

```typescript
constructor(
  private dailyUsageService: DailyUsageService,
) {}

async recordUsage(clientId: number, client: string, upload: number, download: number) {
  await this.dailyUsageService.recordDailyUsage(clientId, client, upload, download);
}
```

### 3. 查询用量数据

```typescript
// 获取每日用量
const dailyUsage = await this.dailyUsageService.getDailyUsage(
  clientId, 
  client, 
  startDate, 
  endDate
);

// 获取用量汇总
const summary = await this.dailyUsageService.getUsageSummary(
  clientId, 
  client, 
  startDate, 
  endDate
);
```

## 性能特性

### 1. 内存缓存优化
- **原子性操作**: 确保并发安全
- **批量同步**: 每30秒批量写入数据库
- **自动清理**: 定时清理过期缓存
- **大小限制**: 最大10000条缓存记录

### 2. 数据库优化
- **UPSERT操作**: 使用MySQL的ON DUPLICATE KEY UPDATE
- **批量处理**: 100条记录为一批
- **索引优化**: 针对查询模式优化索引

### 3. 定时任务
- **缓存同步**: 每30秒同步缓存到数据库
- **强制同步**: 每小时强制同步确保一致性
- **数据清理**: 每天凌晨2点清理90天前的记录

## 配置选项

### 环境变量
```bash
# 内存缓存配置
MEMORY_CACHE_MAX_SIZE=10000
MEMORY_CACHE_TTL_HOURS=48

# 批量处理配置
DAILY_USAGE_BATCH_SIZE=100

# 数据保留期
DAILY_USAGE_RETENTION_DAYS=90
```

### 模块配置
```typescript
// 在模块中配置
@Module({
  imports: [
    UsageStatsModule,
    // 其他模块...
  ],
})
```

## 数据一致性

### 1. 缓存策略
- **写入缓存**: 优先写入内存缓存
- **定时同步**: 定期批量同步到数据库
- **故障恢复**: 缓存失败时直接写数据库

### 2. 查询策略
- **合并数据**: 查询时合并缓存和数据库数据
- **实时性**: 当日数据包含缓存中的最新数据
- **一致性**: 通过定时同步保证最终一致性

## 监控和维护

### 1. 日志监控
- 缓存操作日志
- 数据库同步日志
- 错误和异常日志

### 2. 性能监控
- 缓存命中率
- 同步频率和耗时
- 内存使用情况

### 3. 数据维护
- 自动清理过期数据
- 缓存大小控制
- 数据库索引维护

## 扩展性

### 1. 水平扩展
- 支持多实例部署
- 数据库层面的扩展
- 缓存分片策略

### 2. 功能扩展
- 新增统计维度
- 自定义聚合规则
- 实时统计功能

### 3. 集成扩展
- 与其他监控系统集成
- 数据导出功能
- 报表生成功能

## 最佳实践

### 1. 使用建议
- 异步调用用量记录接口
- 合理设置查询时间范围
- 定期监控缓存状态

### 2. 性能优化
- 避免频繁的大范围查询
- 使用适当的缓存策略
- 定期清理历史数据

### 3. 错误处理
- 实现重试机制
- 监控错误率
- 及时处理异常情况

## 故障排除

### 1. 常见问题
- 缓存内存不足
- 数据库连接问题
- 同步延迟过高

### 2. 解决方案
- 调整缓存大小限制
- 检查数据库连接配置
- 优化同步频率

### 3. 调试工具
- 缓存统计接口
- 性能监控脚本
- 日志分析工具
