import { Injectable } from '@nestjs/common';
import { Observable, Subject } from 'rxjs';
import { filter, map } from 'rxjs/operators';

@Injectable()
export class NotificationService {
  private subject = new Subject<[string, any]>();
  post(name: string, value: any) {
    this.subject.next([name, value]);
  }
  register(name: string): Observable<any>  {
    return this.subject.pipe(filter(i => i[0]  === name )).pipe((map(i => i[1])));
  }
}
