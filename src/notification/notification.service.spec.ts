import { Test, TestingModule } from '@nestjs/testing';
import { NotificationService } from './notification.service';

describe('NotificationService', () => {
  let service: NotificationService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [NotificationService],
    }).compile();
    service = module.get<NotificationService>(NotificationService);
  });
  it('should be defined', (done) => {
    expect(service).toBeDefined();
    service.register('test').subscribe(i => {
      expect(i).toEqual('test');
      done();
    });
    service.post('test', 'test');
  });
});
