import { Injectable } from '@nestjs/common';
import * as dotenv from 'dotenv';
import * as Joi from 'joi';
import * as fs from 'fs';

export interface EnvConfig {
  [key: string]: string;
}

@Injectable()
export class ConfigService {

  private readonly envConfig: { [key: string]: string };

  constructor(filePath: string) {
    const config = dotenv.parse(fs.readFileSync(filePath));
    this.envConfig = ConfigService.validateInput(config);
  }

  // environment variables would override values from .env files with the same key
  get(key: string): string {
    return process.env[key] || this.envConfig[key];
  }

  /**
   * Ensures all needed variables are set, and returns the validated JavaScript object
   * including the applied default values.
   */
  private static validateInput(envConfig: EnvConfig): EnvConfig {
    const envVarsSchema: Joi.ObjectSchema = Joi.object({
      PORT: Joi.number().default(3000),
      DB_HOST: Joi.string(),
      DB_PORT: Joi.number(),
      DB_NAME: Joi.string(),
      DB_USER: Joi.string(),
      DB_PASS: Joi.string(),

      K8S_URL: Joi.string(),
      K8S_USER: Joi.string(),
      K8S_PASS: Joi.string(),
      NODES_COLLECTION: Joi.string(),
      SYNC_CHANNEL: Joi.string(),

      // Product Supported Protocols. The specification is as follows, Product1:Protocol1,Protocol2;Product2:Protocol1
      // Leave blank to support all protocols
      PRODUCT_SUPPORTED_PROTOCOLS: Joi.string(),

      // sort
      PROTOCOL_ORDER: Joi.string(),
      COUNTRY_ORDER: Joi.string(),
      NODE_ORDER: Joi.string(),

      // reset
      USE_EXTERNAL_RESET: Joi.boolean(),
      ENABLE_PROD_HOPS: Joi.boolean().default(true),

      // Redis
      REDIS_URL: Joi.string(),
    });

    const { error, value: validatedEnvConfig } = Joi.validate(
      envConfig,
      envVarsSchema,
    );
    if (error) {
      throw new Error(`Config validation error: ${error.message}`);
    }
    return validatedEnvConfig;
  }
}
