import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import {ConfigService} from './config/config.service';
import { LocaleGuard } from './locale/locale.guard';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    disableErrorMessages: true,
  }));
  const config: ConfigService = app.get(ConfigService);

  // 👇️ handle uncaught exceptions
  process.on('uncaughtException',  (err) => {
    console.log(err);
  });

  app.useGlobalGuards(new LocaleGuard());
  await app.listen(config.get('PORT'));
}
bootstrap();
