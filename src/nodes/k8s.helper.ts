import * as k8s from '@kubernetes/client-node';
import {KubeConfig} from '@kubernetes/client-node';
import * as lodash from 'lodash';
import {Logger} from '@nestjs/common';
import { meta } from 'joi';

export enum PROTOCOLS {
    HTTPS = 'https',
    V2RAY = 'v2ray',
    TROJAN = 'trojan',
    HYSTERIA = 'hysteria',
    SS = 'ss',
}

export interface PodInfo {
    name: string;
    namespace: string;
    ip: string;
    nodeName: string;
    type: string;
    country: string;
}

export interface NodeInfo {
    // msk-hk-12343
    name: string;

    // msk-hk-1
    proxyLabel: string;

    // msk
    proxyLine: string;

    // hk
    country: string;

    // 1, 2
    proxyShard: string;

    // [rubiz,jss]
    project?: string[];

    // 29444
    trojanPort?: string;

    httpPort?: string;

    hysteriaPort?: string;

    tags: string[];

    protocols: string[];
}

/**
 * An abstract of a proxy line, i.e. Misa<PERSON>'s Hong Kong, CM TW
 * A proxy line includes multiple proxies to scale
 */
export interface ProxyLine {
    // msk, pqs
    line: string;

    // hk, tw
    country: string;

    // trojan, http
    type: string;

    proxies: any[];
}

// A helper class to retrieve pods from a k8s cluster
export class K8sHelper {
    private logger: Logger;
    private kubeConfig: KubeConfig;
    private k8sApi: any;
    private nodes: NodeInfo[];
    private protocols: string[] = [];
    private tags: string[] = [];
    private countries: string[] = [];
    private nodesWithSite = [];

    constructor(configFileName: string, tag: string) {
        this.logger = new Logger(`K8sHelper:${tag}`);
        this.kubeConfig = new k8s.KubeConfig();
        this.kubeConfig.loadFromFile(configFileName);
        this.k8sApi = this.kubeConfig.makeApiClient(k8s.CoreV1Api);
    }

    async getNodesWithSites() {
        return this.nodesWithSite;
    }

    // Get labeled nodes
    async getNodes(): Promise<NodeInfo[]> {
        this.nodesWithSite = [];
        const newNodes: NodeInfo[] = [];
        
        // must use new set and then reassign to instance variable to remove old values
        const newTags = new Set<string>();
        const newProtocols = new Set<string>();
        const newCountries = new Set<string>();

        const nodeReq = await this.k8sApi.listNode(null, null, null, null, 'AsNode');
        nodeReq.body.items.map(node => {
            const metadata = node.metadata;
            if (metadata.labels.Delivery === 'yes') {
                const internalIP = node.status.addresses.find(a => a.type === 'InternalIP');
                if (!this.nodesWithSite.some(existingNode => existingNode.address === internalIP.address)) {
                    this.nodesWithSite.push({
                        name: metadata.name,
                        ip: internalIP.address,
                    });
                }
            }

            const [line, country, shard] = metadata.labels.AsNode.split('-');
            newCountries.add(country);

            const nodeTags = metadata.labels.TAGS?.split('-') || [];
            nodeTags.forEach(tag => newTags.add(tag));

            const nodeProtocols = [];
            [{protocol: PROTOCOLS.HTTPS, label: 'HttpProxy'}, {protocol: PROTOCOLS.HYSTERIA, label: 'HysteriaProxy'}, {protocol: PROTOCOLS.TROJAN, label: 'XrayProxy'}].forEach((p: {protocol: string, label: string}) => {
                if (metadata.labels[p.label]) {
                    nodeProtocols.push(p.protocol);
                }
            });
            nodeProtocols.forEach(p => newProtocols.add(p));

            newNodes.push({
                name: metadata.name,
                proxyLabel: metadata.labels.AsNode.toUpperCase(),
                proxyLine: line.toUpperCase(),
                proxyShard: shard,
                country: country.toUpperCase(),
                project: metadata.labels.project?.split('-') || [],
                trojanPort: metadata.labels.trojanPort || '29444',
                httpPort: metadata.labels.PortHttp || '20443',
                hysteriaPort: metadata.labels.PortHttp || '21443',
                tags: nodeTags,
                protocols: nodeProtocols,
            });
        });
        this.nodes = newNodes;
        this.tags = Array.from(newTags);
        this.protocols = Array.from(newProtocols);
        this.countries = Array.from(newCountries);
        return newNodes;
    }

    async getProxyLines(): Promise<ProxyLine[]> {
        let pods = [];
        const nodes = await this.getNodes();
        const nodeNames = nodes.map(node => node.name);
        const requests = await this.k8sApi.listNamespacedPod('default', null, null, null, null, 'proxy-type');
        requests.body.items
            // filter pods only in labeled node
            .filter(p => nodeNames.includes(p.spec.nodeName))
            // filter out not-running pods
            .filter(p => p.status.phase === 'Running')
            // Some pot in terminating status still has .status.phase === 'Running'
            .filter(p => p.metadata.deletionTimestamp === undefined)
            .map(pod => {

                //  hop realm used in iepl to proxy
                if (/hop-realm/.test(pod.metadata.name)) {
                    pods.push(this.parsingPod(pod, PROTOCOLS.HTTPS));
                    pods.push(this.parsingPod(pod, PROTOCOLS.TROJAN));
                    pods.push(this.parsingPod(pod, PROTOCOLS.HYSTERIA));
                    //  hop hy2 used in hops to proxy https and trojans
                } else if (/hop-hy2-client/.test(pod.metadata.name)) {
                    pods.push(this.parsingPod(pod, PROTOCOLS.HTTPS));
                    pods.push(this.parsingPod(pod, PROTOCOLS.TROJAN));
                    pods.push(this.parsingPod(pod, PROTOCOLS.HYSTERIA));
                } else if (/hop-https/.test(pod.metadata.name)) {
                    pods.push(this.parsingPod(pod, PROTOCOLS.HTTPS));
                } else if (/hop-hysteria/.test(pod.metadata.name)) {
                    pods.push(this.parsingPod(pod, PROTOCOLS.HYSTERIA));
                } else  if (/hop-xray/.test(pod.metadata.name)) {
                    pods.push(this.parsingPod(pod, PROTOCOLS.TROJAN));
                } else if (/v2ray-proxy/.test(pod.metadata.name)) {
                    pods.push(this.parsingPod(pod, PROTOCOLS.TROJAN));
                } else if (/hysteria-proxy/.test(pod.metadata.name)) {
                    pods.push(this.parsingPod(pod, PROTOCOLS.HYSTERIA));
                } else if (/caddy-proxy/.test(pod.metadata.name)) {
                    pods.push(this.parsingPod(pod, PROTOCOLS.HTTPS));
                }
            });

        pods = lodash.sortBy(pods, 'name');

        this.logger.log(`update pods info successful, current has ${pods.length} pods`);
        return lodash.chain(pods).groupBy((pod) => `${pod.line}-${pod.country}-${pod.type}`).map((value, key) => {
            const [line, country, type] = key.split('-');
            return {proxies: value, line, country, type};
        }).value();
    }

    // parsing pod
    private parsingPod(pod, type: PROTOCOLS) {
        const name = pod.metadata.name;
        const namespace = pod.metadata.namespace;
        const ip = pod.status.hostIP;
        const nodeName = pod.spec.nodeName;
        const nodeInfo = this.nodes.filter(node => node.name === nodeName).pop();
        const country = nodeInfo?.country || nodeName.match(/(?<=-)\w{2}(?=\w*-)/)?.pop().toUpperCase() || 'HK';
        const shard = nodeInfo?.proxyShard || 1;

        // set up port
        let port: number;
        switch (type) {
            case PROTOCOLS.HTTPS:
                port = +nodeInfo.httpPort;
                break;
            case PROTOCOLS.V2RAY:
                port = 28443;
                break;
            case PROTOCOLS.TROJAN:
                port = +nodeInfo.trojanPort;
                break;
            case PROTOCOLS.HYSTERIA:
                port = +nodeInfo.hysteriaPort;
                break;
            default:
                port = 20443;

        }

        return {
            name: nodeInfo.proxyLabel.toUpperCase(),
            podName: name,
            namespace,
            ip,
            host: `${nodeName}.strongpipe.cloud`,
            published: true,
            country,
            description: nodeName.match(/(?<=-)\w{2}(?=\w*-)/)?.pop().toUpperCase() || 'HK',
            shard,
            nodeName,
            status: 1,
            line: nodeInfo.proxyLine,
            sni: '106-110-192-225.d.cjjd20.com',
            tags: nodeInfo.tags,
            project: nodeInfo?.project,
            type,
            port,
        };
    }

    getProtocols(): string[] {
        return this.protocols;
    }

    getTags(): string[] {
        return this.tags;
    }

    getCountries(): string[] {
        return this.countries;
    }

}
