import { Body, CACHE_MANAGER, Controller, Get, HttpException, Inject, Logger, Param, Post, Put } from '@nestjs/common';
import { IsNotEmpty} from 'class-validator';
import { Cache } from 'cache-manager';
import * as Big from 'big.js';
import { Service, ServiceResetType } from '../service/service.entity';
import { JweService } from '../auth/jwe/jwe.service';
import * as moment from 'moment';
import { ServiceService } from '../service/service.service';
import { DailyUsageService } from '../service/daily-usage.service';
import { post } from 'superagent';
import { NodesService } from './nodes.service';
import * as process from 'process';

export class AuthDTO {
  @IsNotEmpty()
  username: string;

  @IsNotEmpty()
  password: string;
}
export class AuthDTO2 {
  @IsNotEmpty()
  username: string;

  @IsNotEmpty()
  digest: string;

  @IsNotEmpty()
  message: string;
}

export class UsageDTO {

  @IsNotEmpty()
  username: string;

  @IsNotEmpty()
  password: string;

  @IsNotEmpty()
  upload: number;

  @IsNotEmpty()
  download: number;
}

@Controller('api/v1/subscribes')
export class SubscribesController {

  private readonly logger = new Logger(SubscribesController.name);
  constructor(
    private jwe: JweService,
    @Inject('ServiceService')
    private serviceService: ServiceService,
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,
    private nodesService: NodesService,
    private dailyUsageService: DailyUsageService,
  ) {
  }

  @Post('authenticate')
  async auth(@Body() body: AuthDTO) {

    try {
      this.logger.log(`Begin authenticate service ${body.username}:${body.password.slice(body.password.length / 2, body.password.length - 1)} `);
    } catch (e) {
      this.logger.error(`Failed to log authentication info ${body.username} due to ${e.message}`);
    }
    const subscribes = await Service.find({ where: { username: body.username, password: body.password } });
    if (subscribes.length === 0) {
      throw new HttpException({
        error: 'NoSuchUser',
        message: 'We do not recognise this user',
      }, 403);
    }
    const subscribe = subscribes.pop();
    const result = subscribe.toJson();
    result['token'] = await this.jwe.encrypt(result);
    return result;

  }

  @Put(':id')
  async update(@Param('id') id, @Body() body: UsageDTO) {
    // this.logger.log(`Begin update usage for service  ${id}, upload is ${body.upload}, download is ${body.download}`);
    const services = await Service.find({ where: { username: body.username, password: body.password, id} });
    if (services.length === 0) {
      throw new HttpException({
        error: 'NoSuchUser',
        message: 'We do not recognise this user',
      }, 403);
    }
    const service = services.pop();
    const oldUpload = new Big(service.trafficUpload);
    const up = new Big(body.upload);
    const newUpload = oldUpload.plus(up);
    service.trafficUpload = newUpload.toString();

    const oldDownload = new Big(service.trafficDownload);
    const down = new Big(body.download);
    const newDownload = oldDownload.plus(down);
    service.trafficDownload = newDownload.toString();

    // 异步记录每日用量统计，不阻塞主流程
    setImmediate(async () => {
      try {
        await this.dailyUsageService.recordDailyUsage(service, body.upload, body.download);
      } catch (error) {
        this.logger.error(`Failed to record daily usage for service ${service.id}: ${error.message}`);
      }
    });

    // is overage
    const newTotal = newUpload.plus(newDownload);
    const quota = JSON.parse(service.trafficQuota);
    const total = new Big(quota[0].traffic);
    const isOverage = newTotal.minus(total) >= 0;
    if (isOverage) {
      const brand = service.client.split('-')[0];
      const cacheId = `${brand}-${service.clientId}-${+(new Date().getMonth()) + 1}`;
      const isSendOverageEmail = await this.cacheManager.get(cacheId);
      if (!isSendOverageEmail) {
        await this.cacheManager.set(cacheId, '1');
        try {
          const callbackUri = this.nodesService.getCallbackUri(service.client, process.env.NODE_ENV === 'dev');
          await post(callbackUri)
            .set({
              'Content-Type': 'application/json',
            })
            .send({
              action: 'Overuse',
              serviceId: service.clientId,
            });
          this.logger.log(`Service ${service.id} executes overuse hook`);
        } catch (e) {
          this.logger.error(`Service ${service.id} failed to execute overuse hook， due to: ${JSON.stringify(e)}`);
        }
      }
      // api.{namespace}/api/v1/services/{id}/
      if (service.trafficResetCycle === ServiceResetType.Never) {
        service.trafficQuota = JSON.stringify([{traffic: 0, speed: quota[0].speed}]);
        service.trafficUpload = 0;
        service.trafficDownload = 0;
        this.logger.log(`Service ${service.id} has used up all quota, reset to zero`);

        // 处理服务重置时的每日用量
        try {
          await this.dailyUsageService.handleServiceReset(service);
        } catch (error) {
          this.logger.error(`Failed to handle service reset for daily usage of service ${service.id}: ${error.message}`);
        }
      }
    }
    service.lastUpdatedTime = moment.utc().toDate();
    await service.save();
    return service.toJson();
  }

  @Get('online/:host/:type/:online')
  async online(@Param() params) {
    this.logger.log(`proxy ${params.type} of host ${params.host} has ${params.online} users`);
  }

}
