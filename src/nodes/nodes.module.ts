import { CacheModule, Module } from '@nestjs/common';
import { NodesService } from './nodes.service';
import { ConfigModule } from '../config/config.module';
import { SubscribesController } from './subscribes.controller';
import { AuthModule } from '../auth/auth.module';
import { ServiceService } from '../service/service.service';
import { DailyUsageService } from '../service/daily-usage.service';
import { PerformanceMonitorService } from '../service/performance-monitor.service';
import { CommonModule } from '../common/common.module';
import { LocaleModule } from '../locale/locale.module';
import { ConfigService } from '../config/config.service';
import { redisStore } from 'cache-manager-redis-yet';

@Module({
  imports: [ConfigModule, AuthModule, CommonModule, LocaleModule,
    CacheModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        isGlobal: true,
        store: await redisStore({
          url: configService.get('REDIS_URL'),
        }),
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [SubscribesController],
  providers: [NodesService, ServiceService, DailyUsageService, PerformanceMonitorService],
  exports: [NodesService],
})
export class NodesModule {}
