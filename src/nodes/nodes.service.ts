import {Injectable, Logger, OnModuleInit} from '@nestjs/common';
import * as lodash from 'lodash';
import {ConfigService} from '../config/config.service';
import {K8sHelper, ProxyLine} from './k8s.helper';
import { existsSync } from 'node:fs';
import {Firestore} from '@google-cloud/firestore';
import * as  path from 'path';
interface ManualLine  {id: string; line: string; country: string; protocols: string[]; subDomains: string[]; domain: string;  status: number; tags: string[]; }

//  make hop a dictionary
const hops: Map<string, ManualLine>  = new Map();

// the following 3 arrays are used to sort nodes
const protocols = ['https', 'trojan', 'hysteria', 'v2ray', 'ss'];
const ports = {https: 20443, trojan: 29444, hysteria: 21443};
const countries = ['HK', 'TW', 'SG', 'JP', 'US',  'DE', 'UK', 'RU'];
let lines = [ 'NF', 'MSK', 'LM',  'NAT', 'SC', 'BST', 'HOME', 'PQS', 'BN'];

const countryChineseNames = {HK: '香港', JP: '日本', DE: '德国', TW: '台湾', US: '美国', UK: '英国', SG: '新加坡', RU: '俄罗斯', NL: '荷兰'};
const lineChineseNames = {
  CTVIP: 'CN2中转',
  IPLC: '隧道',
  IEPL: '专线',
  CM: '移动',
  CU: '联通',
  CT: '电信',
  DIRECT: '直连',
  MSK: 'MSK',
  BWG: 'BWG',
  HOME: '家寬',
  PQS: 'PQS',
};
const nationalFlag = {HK: '🇭🇰', JP: '🇯🇵', DE: '🇩🇪', TW: '🇨🇳', US: '🇺🇸', UK: '🇬🇧', SG: '🇸🇬', RU: '🇷🇺', KR: '🇰🇷', NL: '🇳🇱'};

const countryCodes =  {UK: 1, US: 2, TW: 3, JP: 4, HK: 5, RU: 6, DE: 7, KR: 8, SG: 9};
const protocolCodes =  {'https': 1, 'v2ray': 2, 'trojan': 3, 'trojan-ws': 4, 'ss': 5};

export interface NodesInfos {
  tags: string[];
  countries: string[];
  protocols: string[];
  countriesMap?: Array<{code: string, name: string}>;
}

@Injectable()
export class NodesService implements OnModuleInit {
  private readonly logger = new Logger(NodesService.name);

  //  make it public to test

  private manualLines: any = [];
  private directLines: any = [];
  private hopLines: any = [];
  private allLines: any = [];

  public countryChineseNames;
  public nationalFlag;
  public nodesWithSites;
  private env = process.env.NODE_ENV;
  private hopK8sHelper: K8sHelper;
  private directK8sHelper = new K8sHelper(`k8s/${this.env}/kubeconfig-${this.env}.yaml`, 'direct');
  private readonly loadHop: boolean = false;
  private firestore: Firestore;
  public nodesInfos: NodesInfos;

  constructor(private config: ConfigService) {
    this.nationalFlag = nationalFlag;
    this.countryChineseNames = countryChineseNames;

    const hopConfigPath = `k8s/${this.env}/kubeconfig-${this.env}-hop.yaml`;
    this.loadHop = existsSync(hopConfigPath);
    if (this.loadHop) {
      this.hopK8sHelper = new K8sHelper(hopConfigPath, 'hop');
    }

    this.firestore = new Firestore({
      projectId: 'aquadata-efa8e',
      keyFilename: path.resolve(__filename, '../../../firebase.json'),
    });
  }

  async getProxyPods(): Promise<void> {
    let nodesInfos: NodesInfos  = {tags: [], protocols: [], countries: []};
    if (this.loadHop) {
      const  hopLines = await this.hopK8sHelper.getProxyLines();
      nodesInfos = {tags: this.hopK8sHelper.getTags(), protocols: this.hopK8sHelper.getProtocols(), countries: this.hopK8sHelper.getCountries()};
      this.hopLines = this.sortProxies(hopLines);
    }

    const directLines = await this.directK8sHelper.getProxyLines();
    nodesInfos.tags = lodash.union(nodesInfos.tags, this.directK8sHelper.getTags());
    nodesInfos.protocols = lodash.union(nodesInfos.protocols, this.directK8sHelper.getProtocols());
    nodesInfos.countries = lodash.union(nodesInfos.countries, this.directK8sHelper.getCountries());
    const countriesMap = nodesInfos.countries.map((country) => ({
      code: country,
      name: countryChineseNames[country.toUpperCase()],
    }));
    this.nodesInfos = {...nodesInfos, countriesMap};
    this.directLines = this.sortProxies(directLines);
    this.nodesWithSites = this.directK8sHelper.getNodesWithSites();
    this.allLines = this.sortProxies(this.hopLines.concat(directLines).concat(this.manualLines));
  }

  onModuleInit(): any {

    this.firestore.collection(`lines`).onSnapshot(docSnapshot => {
      for (const change of docSnapshot.docChanges()) {
        lines =  change.doc.data().lines.split(',');
        this.logger.log(`lines order updated to ${lines}`);
        this.getProxyPods().then();
      }
    });

    const collectionPath =  this.env === 'prod' ? 'manualLines' : 'uat-manualLines';
    this.firestore.collection(collectionPath).onSnapshot(docSnapshot => {
      const changes  =  docSnapshot.docChanges().map(change => {
        const line  = change.doc.data() as  ManualLine;
        line.id = change.doc.id;
        return line;
      });
      changes.forEach(line =>  {
        if (line.status === 1) {
          hops.set(line.id, line);
        } else {
          hops.delete(line.id);
        }
      });
      this.logger.log(`manual lines updates to  ${JSON.stringify([...hops.values()])}`);
      this.generateNodes();
      this.getProxyPods().then();
    });

    this.generateNodes();
    this.getProxyPods().then();
    setInterval(async () => {
      await this.getProxyPods();
    }, 60000);
  }

  generateNodes() {
    this.manualLines = [];
    for (const protocol of protocols) {

      for (const hop of  hops.values()) {

        if (!hop.protocols.includes(protocol)) {
          continue;
        }

        const proxyLine: ProxyLine = {line: hop.line, type: protocol, country: hop.country, proxies: []};
        for (let i = 0; i < hop.subDomains.length; i++) {
          proxyLine.proxies.push({
            // name: `${lineChineseNames[hop.type.split('-')[0]]}-${countryChineseNames[country]}-${i + 1}`,
            name: `${hop.line}-${hop.country}-${i + 1}`,
            host: `${hop.subDomains[i]}.${hop.domain}`,
            port: ports[protocol],
            namespace: `jetstream`,
            type: protocol,
            country: hop.country,
            line: hop.line,
            sni: '106-110-192-225.d.cjjd20.com',
            tags: hop.tags || [],
          });
        }
        this.manualLines.push(proxyLine);
      }
    }
    return this.manualLines;
  }

  shardNodesV2(id: number, direct: boolean = false, allShards: boolean = false) {
    const proxyLines = direct ? this.directLines : this.manualLines;

    const shared =  proxyLines.map((line) => {

      if (allShards) {
        return line.proxies;
      } else {
        const position = id % line.proxies.length;
        return line.proxies[position];
      }
    });

    return lodash.flatten(shared);
  }

  /**
   * To sort an array of objects by their properties of line, country and type.
   * Object can be ProxyLine or Proxy
   * @param proxies
   */
  sortProxies(proxies: [{ line: string; country: string; type: string; }] | ProxyLine[]): any[] {

    const result = [];
    const groupByProtocol = lodash.groupBy(proxies, 'type');
    const sortedProtocols = lodash.keys(groupByProtocol).sort((a, b) => protocols.indexOf(a) - protocols.indexOf(b));

    for (const protocol of sortedProtocols) {
      const groupOfProtocol = groupByProtocol[protocol];

      const groupByCountry  = lodash.groupBy(groupOfProtocol, 'country');
      const sortedCountry = lodash.keys(groupByCountry).sort((a, b) => countries.indexOf(a) - countries.indexOf(b));

      for (const country of sortedCountry) {
        const groupOfCountry = groupByCountry[country];

        const groupByLine = lodash.groupBy(groupOfCountry, 'line');
        const sortedLine = lodash.keys(groupByLine).sort((a, b) => lines.indexOf(a) - lines.indexOf(b));

        for (const line of sortedLine) {
          result.push(groupByLine[line]);
        }
      }
    }
    return  lodash.flatten(result);
  }

  translateNodeNameToChinese(node): string {
    const segment = node.name.split('-');
    const [line, proxyCountry, shard] = segment;
    return `${countryChineseNames[proxyCountry] || proxyCountry}-${lineChineseNames[line] || line}-${shard}`;
  }

  getCallbackUri(brand: string, isDevEnv: boolean = false): string {
    if (isDevEnv) {
      return `http://127.0.0.1/api/v1/helpers/callback?token=e5760917f642f22b271a455061f7b87c`;
    }
    const [product, uatFlag] = brand.split('-');
    let domain = 'api.droplet.today';
    switch (product) {
      case 'flash': domain = 'lightning.cool'; break;
      case 'jetstream': domain = 'jetflow.site'; break;
      case 'lightning': domain = 'api.droplet.today'; break;
      default: domain = 'rubiz.site'; break;
    }
    if (uatFlag) {
      domain = `uat.${domain}`;
    }
    return `https://${domain}/api/v1/helpers/callback?token=e5760917f642f22b271a455061f7b87c`;
  }
}
