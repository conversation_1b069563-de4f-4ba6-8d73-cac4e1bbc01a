import { Test, TestingModule } from '@nestjs/testing';
import { NodesService } from './nodes.service';
import { ConfigModule } from '../config/config.module';
import { AuthModule } from '../auth/auth.module';

describe('PointerService', () => {
  let service: NodesService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [ConfigModule, AuthModule],
      providers: [NodesService],
    }).compile();
    service = module.get<NodesService>(NodesService);
  });

  it('should return the unused ip', () => {

    service.generateNodes();
    console.time('shareNodesV2');
    const pointers = service.shardNodesV2(100004);
    console.timeEnd('shareNodesV2');
    console.log(pointers);

  });
});
