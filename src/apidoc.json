{"name": "Superapi", "version": "1.0.0", "description": "The project name of Lightning expresses the idea that we want to provide users with the fastest and most stable service. It is an infrastructure service abstracted from the earliest series of microservices. This type of service focuses on providing the core function of the product: network proxy Service. The composition of the project can be basically divided into network lines, cluster management, proxy protocol operation, service life cycle management, service authentication, etc.", "title": "Superapi", "url": "https://uat-api.droplet.today"}