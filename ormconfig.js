module.exports = {
  "type": "mysql",
  "host": process.env['DB_HOST']|| "localhost",
  "port": 3307,
  "username": process.env['DB_USER'] ||"root",
  "password": process.env['DB_PASS'] ||"root",
  "database": process.env['DB_NAME'] || "superapi",
  "entities": ["src/**/**.entity{.ts,.js}"],
  "migrations": ["src/database/migrations/*.ts"],
  "cli": {
    "migrationsDir": "src/database/migrations"
  }
}
