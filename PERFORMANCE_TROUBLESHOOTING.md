# 性能问题排查指南

## 问题现象

压力测试显示：
- ✅ 响应时间: 优秀 (<100ms)
- 🔴 成功率: 需要优化 (<95%)

## 可能原因分析

### 1. **并发压力过大**
**原因**: 
- 20个并发用户 × 每秒5个请求 = 100 QPS
- 超出单实例应用的处理能力
- 数据库连接池耗尽

**解决方案**:
```javascript
// 优化后的配置
const CONFIG = {
  concurrentUsers: 10,        // 降低并发数
  requestsPerUserPerSecond: 2, // 降低请求频率
  testDurationMinutes: 1,     // 缩短测试时间
};
```

### 2. **数据库性能瓶颈**
**原因**:
- 30秒同步频率导致频繁数据库写入
- 可能的锁竞争和死锁
- 批量操作冲突

**解决方案**:
```javascript
// 调整同步频率
schedule.scheduleJob('*/2 * * * *', async () => {
  await this.flushCacheToDatabase();
});
```

### 3. **内存缓存竞争**
**原因**:
- 高并发下原子操作竞争
- 缓存大小限制触发频繁清理
- 内存压力导致GC频繁

**解决方案**:
- 增加缓存大小限制
- 优化缓存清理策略
- 监控内存使用

### 4. **网络超时和重试**
**原因**:
- 请求超时设置过短
- 没有重试机制
- 网络抖动影响

**解决方案**:
```javascript
// 添加重试机制
const CONFIG = {
  maxRetries: 3,
  retryDelay: 100,
};

// 增加超时时间
await axios.put(url, data, { timeout: 10000 });
```

## 诊断步骤

### 1. 运行诊断脚本
```bash
node diagnose-performance-issues.js
```

这个脚本会测试：
- 服务器连接
- 服务创建
- 流量更新
- 用量查询
- 并发请求处理
- 内存压力测试

### 2. 检查系统资源
```bash
# 查看系统性能
node scripts/performance-monitor.js

# 持续监控
node scripts/performance-monitor.js --watch
```

### 3. 分析应用日志
```bash
# 查看错误日志
grep "ERROR" logs/application.log | tail -50

# 查看用量统计日志
grep "daily usage" logs/application.log | tail -20
```

### 4. 数据库性能检查
```sql
-- 检查慢查询
SHOW PROCESSLIST;

-- 检查锁等待
SHOW ENGINE INNODB STATUS;

-- 检查表锁
SHOW OPEN TABLES WHERE In_use > 0;
```

## 优化建议

### 1. **应用层优化**

#### 调整缓存配置
```typescript
// memory-cache.service.ts
private readonly MAX_CACHE_SIZE = 20000; // 增加缓存大小
private readonly CACHE_TTL_HOURS = 24;   // 减少TTL
```

#### 优化同步频率
```typescript
// daily-usage.service.ts
// 每2分钟同步，减少数据库压力
schedule.scheduleJob('*/2 * * * *', async () => {
  await this.flushCacheToDatabase();
});
```

#### 增加错误处理
```typescript
try {
  await this.recordDailyUsage(clientId, client, upload, download);
} catch (error) {
  this.logger.error(`Failed to record usage: ${error.message}`);
  // 不抛出异常，避免影响主流程
}
```

### 2. **数据库优化**

#### 连接池配置
```typescript
// app.module.ts
TypeOrmModule.forRoot({
  // ...
  extra: {
    connectionLimit: 20,        // 增加连接数
    acquireTimeout: 60000,      // 获取连接超时
    timeout: 60000,             // 查询超时
  },
});
```

#### 索引优化
```sql
-- 确保有合适的索引
CREATE INDEX idx_daily_usage_lookup 
ON daily_usage (client_id, client, date);

-- 分析表统计信息
ANALYZE TABLE daily_usage;
```

### 3. **压力测试优化**

#### 渐进式压力测试
```javascript
// 从低并发开始，逐步增加
const testConfigs = [
  { users: 5, rps: 1 },   // 5 QPS
  { users: 10, rps: 2 },  // 20 QPS
  { users: 15, rps: 3 },  // 45 QPS
];
```

#### 添加监控指标
```javascript
// 监控关键指标
const metrics = {
  responseTime: [],
  errorRate: 0,
  throughput: 0,
  memoryUsage: process.memoryUsage(),
};
```

### 4. **系统级优化**

#### 操作系统调优
```bash
# 增加文件描述符限制
ulimit -n 65536

# 调整TCP参数
echo 'net.core.somaxconn = 1024' >> /etc/sysctl.conf
```

#### Node.js 调优
```bash
# 增加内存限制
node --max-old-space-size=4096 app.js

# 启用性能分析
node --inspect app.js
```

## 监控和告警

### 1. **关键指标监控**
- 响应时间 (< 500ms)
- 成功率 (> 99%)
- 内存使用率 (< 80%)
- CPU使用率 (< 70%)
- 数据库连接数

### 2. **告警设置**
```javascript
// 设置告警阈值
const ALERT_THRESHOLDS = {
  responseTime: 1000,    // 1秒
  errorRate: 0.05,       // 5%
  memoryUsage: 0.8,      // 80%
};
```

### 3. **日志监控**
```bash
# 监控错误日志
tail -f logs/application.log | grep ERROR

# 监控性能日志
tail -f logs/application.log | grep "daily usage"
```

## 故障恢复

### 1. **自动恢复机制**
- 缓存失败时回退到直接数据库操作
- 数据库连接失败时的重试机制
- 内存不足时的自动清理

### 2. **手动恢复步骤**
1. 重启应用服务
2. 清理缓存数据
3. 检查数据库状态
4. 验证功能正常

### 3. **数据一致性检查**
```sql
-- 检查数据一致性
SELECT client_id, client, date, COUNT(*) as cnt
FROM daily_usage 
GROUP BY client_id, client, date 
HAVING cnt > 1;
```

## 总结

成功率低的主要原因通常是：
1. **并发压力过大** - 降低并发数和请求频率
2. **数据库瓶颈** - 优化查询和连接池
3. **内存压力** - 调整缓存策略
4. **网络问题** - 增加超时和重试

建议按以下顺序排查：
1. 运行诊断脚本确定具体问题
2. 调整压力测试参数
3. 优化应用配置
4. 监控系统资源
5. 逐步增加压力测试强度

通过系统性的排查和优化，可以显著提高系统的稳定性和性能。
