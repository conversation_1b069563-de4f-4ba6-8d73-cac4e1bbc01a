kind: Deployment
apiVersion: apps/v1
metadata:
  labels:
    k8s-app: superapi
  name: superapi
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
  selector:
    matchLabels:
      k8s-app: superapi
  template:
    metadata:
      labels:
        k8s-app: superapi
    spec:
      imagePullSecrets:
        - name: gcr-lightning
      containers:
        - name: superapi
          image: gcr.io/lightning-298213/superapi:latest
          env:
            - name: NODE_ENV
              value: uat
            - name: DB_HOST
              valueFrom:
                secretKeyRef:
                  name: dbsecret
                  key: DB_HOST
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: dbsecret
                  key: DB_USER
            - name: DB_PASS
              valueFrom:
                secretKeyRef:
                  name: dbsecret
                  key: DB_PASS
            - name: DB_NAME
              valueFrom:
                secretKeyRef:
                  name: dbsecret
                  key: DB_NAME
            - name: PORT
              value: '80'
            - name: DB_PORT
              value: '3306'
          readinessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 3
            periodSeconds: 3
          resources:
            requests:
              cpu: '110m'
              memory: '220Mi'
            limits:
              cpu: '135m'
              memory: '300Mi'


---

kind: Service
apiVersion: v1
metadata:
  name: superapi
  labels:
    k8s-app: superapi
spec:
  ports:
    - name: superapi-tcp-80
      protocol: TCP
      port: 80
      targetPort: 80
  selector:
    k8s-app: superapi
  type: ClusterIP
